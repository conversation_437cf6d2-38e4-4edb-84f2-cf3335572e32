﻿using System.Data;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.Data.Entity;
using ServiceStack.OrmLite;

namespace KiotViet.Repository.Impl
{
    public class PurchaseOrderDetailRepository : BaseRepository<PurchaseOrderDetail>
    {
        public PurchaseOrderDetailRepository(IDbConnection db) : base(db)
        {
        }

        public async Task<PurchaseOrderDetail> GetByPurchaseProduct(long docId, long productId, CancellationToken cancellationToken = default)
        {
            return await Db.SingleAsync(
                Db.From<PurchaseOrderDetail>().Where(x => x.PurchaseId == docId && x.ProductId == productId && x.MasterProductId == null),
                cancellationToken);
        }

    }
}