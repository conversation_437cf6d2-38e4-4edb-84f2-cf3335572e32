﻿using System;
using System.Threading.Tasks;
using KiotViet.AutoCustomerGroup.Services.Model;

namespace KiotViet.AutoCustomerGroup.Services.Interface
{
    public interface IAutoCgService
    {
        Task ProcessCustomerInfoChange(CustomerChangeInfo customerInfo);

        Task BulkProcessCustomerInfoChange(CustomerBulkChangeInfo bulkChangeInfo);

        Task ProcessGroupChange(CustomerGroupChangeInfo groupChangeInfo);

        Task<int> AssignGroupByScanBirthday(BirthdayRefactorRequest bfi, Action<string> onErrorAction);

        Task<int> UpdateMissingCustomerSummaryInfo(MissingInfoScanRequest scanRequest, Action<string> onErrorAction);
    }
}