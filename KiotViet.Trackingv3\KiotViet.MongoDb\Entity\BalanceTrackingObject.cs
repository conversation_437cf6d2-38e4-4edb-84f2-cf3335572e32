﻿using System;
using System.Collections.Generic;
using System.Linq;
using KiotViet.Data.Entity;
using KiotViet.MongoDb.Interface;
using KiotViet.Services.Common;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class BalanceTrackingObject : IEntityId, ICreatedOn, ITimeMesure, IHistory, IEventType, ITrackingStep, ITotalTime
    {
        //db.BalanceTrackingObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.BalanceTrackingObject.createIndex( { "Data.RetailerId": 1, "Data.DocumentId": 1, "Data.DocumentType": 1 })
        //db.BalanceTrackingObject.createIndex( { "Data.RetailerId": 1, "Data.PartnerId": 1, "Data.DataZone": 1 })
        public BalanceTrackingObject()
        {
            Steps = new List<TrackingStep>();
        }

        public BalanceTrackingObject(IEnumerable<BalanceImpact> listBalanceImpacts)
        {
            Steps = new List<TrackingStep>();
            Data = new List<BalanceTracking>();
            if (listBalanceImpacts == null) return;
            foreach (var item in listBalanceImpacts.Select(x => x.Track))
            {
                Data.Add(new BalanceTracking
                {
                    PartnerId = item.PartnerId,
                    DocumentId = item.DocumentId,
                    DocumentCode = item.DocumentCode,
                    DocumentType = item.DocumentType,
                    Description = item.Description,
                    Value = item.Value,
                    Balance = item.Balance,
                    RetailerId = item.RetailerId,
                    TransDate = item.TransDate,
                    DataZone = item.DataZone
                });
            }
        }

        public ObjectId _id { get; set; }
        public DateTime CreatedAt { get; set; }
        public string TimeMesure { get; set; }
        public string History { get; set; }
        public List<BalanceTracking> Data { get; set; }
        public List<TrackingStep> Steps { get; set; }
        public string EventType => nameof(BalanceTrackingObject);
        public double TotalTime { get; set; }
    }
}