﻿using System;
using System.Threading.Tasks;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.AutoCustomerGroup.Services.Model;
using KiotViet.Util;
using Microsoft.Extensions.Logging;

namespace KiotViet.AutoCustomerGroup.Background.Tasks
{
    public class CustomerGroupBirthdateScanJob : BaseDailyJob<CustomerGroupBirthdateScanJob>
    {
        protected override string TimeExecuteConfigKey { get; set; } = "CustomerGroupBirthdateScanTime";
        private readonly IAutoCgService _autoCgService;

        public CustomerGroupBirthdateScanJob(ILogger<CustomerGroupBirthdateScanJob> logger, IAutoCgService autoCgService) : base(logger)
        {
            _autoCgService = autoCgService;
        }

        protected override async Task ExecuteJob(DateTime time)
        {
            var connectionConfig = KvAppConfig.KvTrackingConfigurationBuilder["CustomerGroupBirthDayScanConns"];
            if (string.IsNullOrEmpty(connectionConfig))
            {
                return;
            }

            var connectionStrings = connectionConfig.Split(",");
            foreach (var connection in connectionStrings)
            {
                try
                {
                    var refactorRequest = new BirthdayRefactorRequest
                    {
                        StartDate = time.Day,
                        StartMonth = time.Month,
                        EndDate = time.Day,
                        EndMonth = time.Month,
                        ConnectionString = connection,
                    };
                    var bithdateCount = await _autoCgService.AssignGroupByScanBirthday(refactorRequest, error =>
                    {
                        Logger.LogError(error);
                    });
                    Logger.LogInformation($"Processed {bithdateCount} customer on {connection}");
                }
                catch (Exception e)
                {
                    Logger.LogError(e.ToString());
                }
            }
        }
    }
}