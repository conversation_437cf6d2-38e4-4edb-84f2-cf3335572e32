﻿using System;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using System.Linq;
using KiotViet.AutoCustomerGroup.Background.Tasks;
using KiotViet.AutoCustomerGroup.Services.Impl;
using KiotViet.Service.Helper;
using KiotViet.Util;
using Microsoft.Extensions.Logging;
using ServiceStack;
using ServiceStack.Data;
using ServiceStack.OrmLite;
using ServiceStack.OrmLite.SqlServer.Converters;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.AutoCustomerGroup.Services.Model;
using KiotViet.AutoCustomerGroup.Background.CustomerGroupConsumers;
using KiotViet.AutoCustomerGroup.Background.Extensions;
using KiotVietFnB.Redis.Extensions;

namespace KiotViet.AutoCustomerGroup.Background

{
    public class Startup
    {
        private readonly IConfiguration _config;
        private readonly ILogger _logger;

        public Startup(IConfiguration config, ILogger<Startup> logger)
        {
            _config = config;
            _logger = logger;
            _logger.LogInformation("Starting..");
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            Licensing.RegisterLicense(KvAppConfig.KvTrackingConfigurationBuilder["servicestack:license"]);

            RegSqlDependency(services);
            services.AddRedisCaching(_config);
            services.AddAuditTrailService(_config);
            services.AddScoped<ICustomerGroupHandler<CustomerBulkChangeInfo>, CustomerBulkChangeInfoHandler>();
            services.AddScoped<ICustomerGroupHandler<CustomerGroupChangeInfo>, CustomerGroupChangeInfoHandler>();
            services.AddScoped<ICustomerGroupHandler<CustomerChangeInfo>, CustomerChangeInfoHandler>();
            services.AddSingleton<IHostedService, CustomerGroupBirthdateScanJob>();
            services.AddSingleton<IHostedService, CustomerSummaryMissingScanJob>();

            services.AddScoped<IAutoCgService, AutoCgService>();
            services.AddScoped<ICustomerGroupService, CustomerGroupService>();
            services.AddScoped<IAuditTrailService, AuditTrailService>();
            services.AddScoped<IRetailerService, RetailerService>();
            services.AddScoped<IUserService, UserService>();
            services.AddKafKaConsumer(_config);
            services.AddHostedService<CustomerGroupConsumerBackground>();

        }

        private void RegSqlDependency(IServiceCollection services)
        {
            OrmLiteConfig.CommandTimeout = 60;
            // register DB connections
            var connectionFactory = new OrmLiteConnectionFactory(KvStatic.DefaultConnStr, SqlServer2016Dialect.Provider);
            var listConn = KvStatic.ConnectionStringDictionary.ToList();
            foreach (var conn in listConn)
            {
                connectionFactory.RegisterConnection(conn.Key, conn.Value, SqlServer2016Dialect.Provider);
            }
            var converter = OrmLiteConfig.DialectProvider.GetStringConverter();
            converter.UseUnicode = true;
            services.AddSingleton<IDbConnectionFactory>(z => connectionFactory);
            // register convert datetime to datetime2
            SqlServer2016Dialect.Provider.RegisterConverter<DateTime>(new SqlServerDateTime2Converter());
        }

        public void Configure(IApplicationBuilder app, IHostingEnvironment env)
        {
        }
    }
}