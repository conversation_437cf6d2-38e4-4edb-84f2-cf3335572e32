﻿using KiotVietFnB.Kafka.AsyncConsumer.Interfaces;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace KiotViet.AutoCustomerGroup.Background.CustomerGroupConsumers
{
    public class CustomerGroupConsumerBackground : BackgroundService
    {
        private readonly ILogger<CustomerGroupConsumerBackground> _logger;
        private readonly IKafkaConsumer<string, string> _kafkaConsumer;
        public CustomerGroupConsumerBackground(
            ILogger<CustomerGroupConsumerBackground> logger
           , IKafkaConsumer<string, string> kafkaConsumer)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _kafkaConsumer = kafkaConsumer ?? throw new ArgumentNullException(nameof(kafkaConsumer));
        }
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            try
            {
                _logger.LogInformation("Starting CustomerInfo Service...");
                await _kafkaConsumer.RunAsync(stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, ex.Message);
            }
        }
    }
}
