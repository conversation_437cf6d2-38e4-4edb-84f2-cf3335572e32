# runtime image
FROM mcr.microsoft.com/dotnet/aspnet:6.0 AS base
WORKDIR /app

# Build
FROM mcr.microsoft.com/dotnet/sdk:6.0 AS build
WORKDIR /
COPY [".", "."]

RUN dotnet restore "./KiotViet.Trackingv3/KiotViet.AutoCustomerGroup.Background/KiotViet.AutoCustomerGroup.Background.csproj"
COPY . /build
WORKDIR /build
RUN dotnet build "./KiotViet.Trackingv3/KiotViet.AutoCustomerGroup.Background/KiotViet.AutoCustomerGroup.Background.csproj" -c Release -o /app

# Publish 
FROM build AS publish
RUN dotnet publish "./KiotViet.Trackingv3/KiotViet.AutoCustomerGroup.Background/KiotViet.AutoCustomerGroup.Background.csproj" -c Release -o /app

# Deployment
FROM base AS final

WORKDIR /app

COPY --from=publish /app .

ENTRYPOINT ["dotnet", "KiotViet.AutoCustomerGroup.Background.dll"]