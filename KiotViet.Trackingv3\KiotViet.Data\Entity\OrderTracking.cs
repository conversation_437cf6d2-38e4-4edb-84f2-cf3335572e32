﻿using System;
using System.Collections.Generic;
using KiotViet.Data.Interface;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class OrderTracking : IRetailerId
    {
        public OrderTracking() { }
        public OrderTracking(OrderTracking source) : this()
        {
            CopyFrom(source);
        }
        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        [PrimaryKey]
        public long ProductId { get; set; }
        [PrimaryKey]
        public long DocumentId { get; set; }
        [PrimaryKey]
        public int DocumentType { get; set; }
        public string DocumentCode { get; set; }
        [PrimaryKey]
        public long OrderId { get; set; }
        public DateTime TransDate { get; set; }
        public double Quantity { get; set; }
        public double Reserved { get; set; }
        public double EndReserved { get; set; }

        public void CopyFrom(OrderTracking source)
        {
            RetailerId = source.RetailerId;
            BranchId = source.BranchId;
            ProductId = source.ProductId;
            DocumentId = source.DocumentId;
            DocumentType = source.DocumentType;
            DocumentCode = source.DocumentCode;
            OrderId = source.OrderId;
            TransDate = source.TransDate;
            Quantity = source.Quantity;
            Reserved = source.Reserved;
            EndReserved = source.EndReserved;
        }

        public void ChangeTransDate(DateTime d)
        {
            TransDate = d;
        }
    }

    public class DummyOrderTracking : OrderTracking
    {
    }
}