﻿using System;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class ProductSerial
    {
        [PrimaryKey]
        public long ProductId { get; set; }
        [PrimaryKey]
        public string SerialNumber { get; set; }
        public int Status { get; set; }
        [PrimaryKey]
        public int BranchId { get; set; }
        [PrimaryKey]
        public int RetailerId { get; set; }
        public double? Quantity { get; set; }
        public long? DocumentId { get; set; }
        public int? DocumentType { get; set; }
        public DateTime? ExpireDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }
}