﻿using System.Data;
using System.Threading.Tasks;
using KiotViet.Data.Entity;
using ServiceStack.OrmLite.Dapper;

namespace KiotViet.Repository.Impl
{
    public class RetailerRepository : BaseRepository<Retailer>
    {
        public RetailerRepository(IDbConnection db) : base(db)
        {
        }

        public async Task<Retailer> GetById(int retailerId)
        {
            var sql = @"
                SELECT r.Id ,
                       r.Language,
                       r.GroupId,
                       r<PERSON>,
                       r.CountryId
                FROM   dbo.Retailer AS r
                WHERE  r.Id = @retailerId;
            ";
            return await Db.QueryFirstOrDefaultAsync<Retailer>(sql, new {retailerId});
        }
    }
}