﻿using System;
using System.Collections.Generic;

// ReSharper disable once CheckNamespace Match namespace is require for audit trail service deserialized
namespace KiotViet.Audit.Model.Entity
{
    public class AuditTrailLog
    {
        public DateTime CreatedDate { get; set; }
        public long UserId { get; set; }
        public int BranchId { get; set; }
        public int? RetailerId { get; set; }
        public int FunctionId { get; set; }
        public string FunctionName { get; set; }
        public int Action { get; set; }
        public string ActionName { get; set; }
        public int GroupId { get; set; }
        public string UserName { get; set; }
        public string Content { get; set; }
        public string SubContent { get; set; }
        public string IpSource { get; set; }
        public string Host { get; set; }
        public string ClientInfo { get; set; }
        public string KeyWords { get; set; }
        public bool IsLoading { get; set; }
        public DateTime? LogDate { get; set; }
        public string RetailerCode { get; set; }
        public int? RefId { get; set; }
        public int IndustryId { get; set; }
        public string IndustryName { get; set; }
        public bool IsMultiCode { get; set; }
        public IList<string> MultiCode { get; set; }
    }
}