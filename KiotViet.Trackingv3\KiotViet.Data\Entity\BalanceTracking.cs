﻿using ServiceStack.DataAnnotations;
using System;

namespace KiotViet.Data.Entity
{
    public class BalanceTracking
    {
        public BalanceTracking() { }
        public BalanceTracking(BalanceTracking source) : this()
        {
            CopyFrom(source);
        }
        [PrimaryKey]
        public long PartnerId { get; set; }
        [PrimaryKey]
        public long DocumentId { get; set; }
        public string DocumentCode { get; set; }
        [PrimaryKey]
        public int DocumentType { get; set; }
        public string Description { get; set; }
        public decimal Value { get; set; }
        public decimal Balance { get; set; }
        public int RetailerId { get; set; }
        public System.DateTime TransDate { get; set; }
        [Ignore]
        public DateTime? OriginalTransDate { get; set; }
        [PrimaryKey]
        public int DataZone { get; set; }
        public void CopyFrom(BalanceTracking source)
        {
            PartnerId = source.PartnerId;
            DocumentId = source.DocumentId;
            DocumentCode = source.DocumentCode;
            DocumentType = source.DocumentType;
            Description = source.Description;
            Value = source.Value;
            Balance = source.Balance;
            RetailerId = source.RetailerId;
            TransDate = source.TransDate;
            DataZone = source.DataZone;
        }
    }
    public class DummyBalanceTracking : BalanceTracking
    {
    }
}