using System.ComponentModel;

namespace KiotViet.Util
{
    public partial class BunchOfEnum
    {

        public enum ProductType
        {
            [Description("Combo - đóng gói")]
            Manufactured = 1,
            [Description("Hàng hóa")]
            Purchased = 2,
            [Description("Dịch vụ")]
            Service = 3,
            [Description("Combo tùy chọn")]
            Customizable = 4
        }
        public enum BalanceTrackType
        {
            Customer = 0,
            Supplier = 1,
            Delivery = 2,
            Employee = 3
        }
        public enum PointTrackType
        {
            Customer = 0,
            Supplier = 1,
            Delivery = 2
        }
        public enum DocumentType
        {
            [Description("Bán hàng")]
            Invoice = 1,
            [Description("Nhập hàng")]
            PurchaseInvoice = 2,
            [Description("Kiểm hàng")]
            StockTake = 3,
            [Description("Chuyển hàng")]
            Tranfer = 4,
            [Description("Nhận hàng")]
            Receive = 5,
            [Description("Trả hàng")]
            Refund = 6,
            [Description("<PERSON><PERSON> hàng [Combo - Đóng gói]")]
            Combo = 7,
            [Description("Trả hàng [Combo - Đóng gói]")]
            RefundCombo = 8,
            [Description("Trả hàng nhà cung cấp")]
            PurchaseReturn = 9,
            [Description("Sản xuất")]
            Manufacturing = 10,
            [Description("Sản xuất [Giảm thành phần]")]
            ManufacturingMaterial = 11,
            [Description("Xuất hủy")]
            DamageItem = 12,
            [Description("Cập nhật giá vốn")]
            ChangeCost = 13,
            [Description("Không giao được hàng")]
            DeliveryFailed = 14,
            [Description("Không giao được hàng [Combo - Đóng gói]")]
            DeliveryFailedCombo = 15,
            [Description("Đặt hàng nhà cung cấp")]
            OrderSupplier = 16,
            [Description("Đặt hàng")]
            Order = 17,
            [Description("Bán hàng [Combo tùy chọn]")]
            CustomizebleCombo = 18,
            [Description("Trả hàng [Combo tùy chọn]")]
            RefundCustomizebleCombo = 19,
            [Description("Không giao được hàng [Combo tùy chọn]")]
            DeliveryFailedCustomizebleCombo = 20,

        }
        public enum PaymentDocType
        {
            [Description("Thanh toán")]
            Payment = 0,
            [Description("Điều chỉnh")]
            ClosingLoanBook = 1,
            [Description("Nhập hàng")]
            Buy = 2,
            [Description("Bán hàng")]
            Sell = 3,
            [Description("Trả hàng")]
            Return = 4,
            [Description("Trả hàng nhà cung cấp")]
            PurchaseReturn = 5,
            [Description("Hoàn trả tạm ứng")]
            ReturnDeposit = 6,
            [Description("Thanh toán(Sổ quỹ)")]
            PaymentViaCashflow = 7,
            [Description("Khác")]
            Miscellaneous = 8,
            [Description("Thanh toán bằng điểm")]
            PaymentViaRewardPoint = 9,
            [Description("Không giao được hàng")]
            DeliveryFailed = 10,
            [Description("Dư nợ đầu kỳ")]
            BeginDebt = 11,
            [Description("Phiếu lương")]
            Payslip = 12
        }

        public enum RewardPointDocType
        {
            [Description("Điều chỉnh")]
            ClosingLoanBook = 0,
            [Description("Bán hàng")]
            Sell = 1,
            [Description("Trả hàng")]
            Return = 2,
            [Description("Thanh toán bằng điểm")]
            PaymentViaRewardPoint = 3,
            [Description("Không giao được hàng")]
            DeliveryFailed = 4
        }
    }
}