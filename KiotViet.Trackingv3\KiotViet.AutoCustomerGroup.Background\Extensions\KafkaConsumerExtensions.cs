﻿using KiotViet.AutoCustomerGroup.Background.CustomerGroupConsumers;
using KiotVietFnB.Kafka.Extensions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace KiotViet.AutoCustomerGroup.Background.Extensions
{
    public static class KafkaConsumerExtensions
    {
        public static void AddKafKaConsumer(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddIgnoreErrorKafkaConsumer<string, string, CustomerGroupConsumerHandler>(configuration);
            services.AddScoped<CustomerGroupConsumerHandler>();
        }
    }
}
