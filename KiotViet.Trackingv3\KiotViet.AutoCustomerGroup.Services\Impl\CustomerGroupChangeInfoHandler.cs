﻿using Confluent.Kafka;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.AutoCustomerGroup.Services.Model;
using ServiceStack;
using System;
using System.Threading.Tasks;

namespace KiotViet.AutoCustomerGroup.Services.Impl
{
    public class CustomerGroupChangeInfoHandler : ICustomerGroupHandler<CustomerGroupChangeInfo>
    {
        private readonly IAutoCgService _AutocgService;
        public CustomerGroupChangeInfoHandler(IAutoCgService autoCgService)
        {
            _AutocgService = autoCgService ?? throw new ArgumentNullException(nameof(autoCgService));
        }
        public async Task ProcessMessage(ConsumeResult<string, string> message)
        {
            var customerInfo = message.Value.FromJson<CustomerGroupChangeInfo>();
            await _AutocgService.ProcessGroupChange(customerInfo);
        }
    }
}
