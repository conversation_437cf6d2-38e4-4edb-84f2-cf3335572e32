﻿namespace KiotViet.MongoDb.Entity
{
    public class TrackingStep
    {
        public string Name { get; set; }
        public double Duration { get; set; }
    }

    public enum InventoryTrackingStep
    {
        TimeGetProduct,
        TimeUpdateItself,
        TimeUpdateAffected,
        TimeUpdateOnHand
    }

    public enum BalanceTrackingStep
    {
        TimeUpdateItself,
        TimeUpdateLastDebit
    }

    public enum BatchExpireImpactStep
    {
        TimeUpdateItself,
        TimeUpdateAffected,
        TimeReUpdateOnHand,
        TimeDeleteItself,
    }

    public enum ComboTrackingStep 
    {
        TimeProcessAllMaterials,
        TimeProcessAllCombos
    }

    public enum OrderTrackingStep
    {
        TimeUpdateItself,
        TimeUpdateAffected,
        TimeUpdateOnHand
    }

    public enum PointTrackingStep
    {
        TimeUpdateItself,
        TimeUpdateAffected,
        TimeUpdateLastDebit
    }

    public enum StockTrackingStep
    {
        TimeUpdateItself,
    }
}
