# Tài liệu Service Theo dõi Tồn kho

## Tổng quan

Service Theo dõi <PERSON> (Inventory Tracking Service) chịu trách nhiệm theo dõi các thay đổi tồn kho sản phẩm trong hệ thống bán lẻ KiotViet. <PERSON><PERSON>, xử lý và duy trì lịch sử đầy đủ của tất cả các giao dịch liên quan đến tồn kho, đả<PERSON> b<PERSON><PERSON> mức tồn kho chính xác và cung cấp dữ liệu cho báo cáo và phân tích.

## C<PERSON>c thành phần chính

### 1. <PERSON><PERSON><PERSON>r<PERSON> xử lý Theo d<PERSON> (Tracking Handlers)

Hệ thống sử dụng các trình xử lý chuyên biệt để xử lý các tác động tồn kho khác nhau:

#### InventoryTrackingHandler (Trình xử lý Theo dõ<PERSON> Tồ<PERSON> kho)
- Xử lý các tác động tồn kho liên quan đến thay đổi số lượng sản phẩm
- <PERSON> dõi các chuyển động hàng tồn kho vật lý thực tế
- Xử lý các hoạt động như mua hàng, bán hàng, điều chỉnh, chuyển kho
- Duy trì lịch sử của tất cả các chuyển động số lượng sản phẩm

#### StockTrackingHandler (Trình xử lý Theo dõi Kho)
- Xử lý các tác động kho liên quan đến thay đổi đặt trước
- Quản lý số lượng đặt trước tách biệt với tồn kho vật lý thực tế
- Đảm bảo tính toán sẵn có chính xác (OnHand - tồn kho thực vs Reserved - đặt trước)

#### ComboTrackingHandler (Trình xử lý Theo dõi Combo)
- Chuyên xử lý các tác động của sản phẩm combo/bộ
- Quản lý theo dõi tồn kho cho các sản phẩm gồm nhiều thành phần
- Đảm bảo tồn kho thành phần được cập nhật khi sản phẩm combo thay đổi

#### ManufacturingTrackingHandler (Trình xử lý Theo dõi Sản xuất)
- Xử lý các tác động tồn kho từ quy trình sản xuất
- Theo dõi tiêu thụ nguyên liệu thô và sản xuất thành phẩm
- Quản lý thay đổi tồn kho trong quá trình lắp ráp hoặc sản xuất

### 2. Các thành phần Logic nghiệp vụ

#### TrackingHelper (Trợ giúp Theo dõi)
Triển khai cốt lõi chứa logic nghiệp vụ theo dõi tồn kho chính:

- **ProcessInventoryImpact**: Phương thức chính để xử lý các thay đổi liên quan đến tồn kho
  - Xác thực và xử lý các event tác động tồn kho
  - Cập nhật bản ghi theo dõi
  - Duy trì lịch sử tồn kho cho sản phẩm
  - Cập nhật mức tồn kho hiện tại

- **AddOrUpdateTrackingAsync**: Tạo hoặc cập nhật bản ghi theo dõi tồn kho
  - Xử lý các thay đổi tồn kho dựa trên chứng từ
  - Tính toán và duy trì giá trị tồn cuối
  - Cập nhật thông tin giá vốn cho sản phẩm

- **UpdateInventorySinceAsync**: Tính toán lại giá trị tồn kho cho các bản ghi bị ảnh hưởng
  - Cập nhật tất cả các bản ghi theo dõi sau bản ghi đã thay đổi
  - Đảm bảo tính nhất quán theo thứ tự thời gian của dữ liệu tồn kho
  - Xử lý các xung đột dựa trên thời gian giao dịch

- **UpdateOnHandAsync**: Cập nhật số lượng có sẵn hiện tại cho sản phẩm
  - Duy trì số lượng tồn kho hiện có trong hệ thống tồn kho
  - Cập nhật thông tin giá vốn và giá bán

- **DeleteTrackingAsync**: Xóa bản ghi theo dõi tồn kho
  - Xử lý việc xóa các giao dịch tồn kho lỗi hoặc đã hủy
  - Đảm bảo tính toán lại tồn kho đúng sau khi xóa

#### Các Trợ giúp khác
- **BalanceTrackingHelper**: Quản lý theo dõi cân đối tài chính liên quan đến tồn kho
- **PointTrackingHelper**: Theo dõi điểm thưởng khách hàng kết nối với giao dịch tồn kho
- **ProductService**: Cung cấp dữ liệu sản phẩm cần thiết cho xử lý tồn kho

### 3. Luồng dữ liệu

1. **Nhận event**: Hệ thống nhận các event tác động tồn kho từ nhiều nguồn
2. **Xử lý tác động**: Các event này được xử lý bởi các trình xử lý thích hợp
3. **Cập nhật Theo dõi**: Bản ghi trong hệ thống theo dõi được tạo/cập nhật
4. **Cập nhật Liên quan**: Các bản ghi liên quan bị ảnh hưởng bởi thay đổi được cập nhật
5. **Cập nhật Số lượng Cuối cùng**: Số lượng OnHand và Reserved hiện tại được cập nhật
6. **Thông báo**: Các sự kiện được xuất bản cho hệ thống downstream

## Tích hợp Service

### 1. Input của Service

#### Input Dựa trên Event
- **Event Kafka**: Service tiêu thụ các event tác động tồn kho từ các chủ đề Kafka
  - Event StockImpact cho thay đổi đặt trước
  - Event InventoryImpact cho thay đổi tồn kho vật lý
  - Event ComboImpact cho thay đổi sản phẩm combo/bộ
  - Event ManufacturingImpact cho quy trình sản xuất

#### Input Cấu hình
- **AppSettings**: Tham số cấu hình cho hành vi theo dõi
- **Cấu hình Retailer**: Cài đặt theo Retailer cho hành vi theo dõi
- **Cờ tính năng**: Cờ tính năng Unleash để kiểm soát chức năng theo dõi

### 2. Output từ Service

#### Cập nhật Cơ sở dữ liệu
- **Thay đổi Cơ sở dữ liệu SQL**: 
  - Cập nhật bảng InventoryTracking
  - Thay đổi bảng ProductBranch cho mức tồn kho hiện tại
  - Cập nhật thông tin giá vốn

#### Tài liệu MongoDB
- **Đối tượng Theo dõi**: Lưu trữ lịch sử theo dõi trong bộ sưu tập MongoDB
  - InventoryTrackingObject
  - StockTrackingObject
  - ComboTrackingObject
  - ManufacturingTrackingObject

#### Sự kiện Đã xuất bản
- **Event Kafka**: Xuất bản sự kiện để downstream tiêu thụ
- **Thông báo RabbitMQ**: Gửi thông báo về thay đổi tồn kho

### 3. Tích hợp với Các Service Khác

#### Tích hợp Cơ sở dữ liệu
- **Cơ sở dữ liệu SQL Chia sẻ**: 
  - Đọc từ bảng sản phẩm và Retailer
  - Cập nhật bảng liên quan đến tồn kho
  - Sử dụng chuỗi kết nối động để truy cập cơ sở dữ liệu dành riêng cho Retailer

- **MongoDB**: 
  - Lưu trữ lịch sử theo dõi và ID event đã xử lý
  - Sử dụng cho nhật ký kiểm toán và lịch sử giao dịch theo dõi

#### Tích hợp Dựa trên Event

- **Tích hợp Kafka**:
  - **Consumer**: Tiêu thụ event tác động tồn kho từ hệ thống upstream
    - Hệ thống Điểm bán hàng
    - Hệ thống Quản lý Đơn hàng
    - Mô-đun Sản xuất
  - **Producer**: Xuất bản sự kiện cập nhật tồn kho cho hệ thống downstream
    - Service báo cáo
    - Service phân tích
    - Service thông báo

- **Tích hợp RabbitMQ**:
  - Gửi thông báo về thay đổi tồn kho
  - Giao tiếp với các microservice khác trong hệ sinh thái

#### Tích hợp Service với Service

- **Redis Cache**: 
  - Lưu trữ cache thông tin sản phẩm và Retailer
  - Cải thiện hiệu suất bằng cách giảm truy vấn cơ sở dữ liệu

- **Service Unleash**:
  - Chuyển đổi tính năng để triển khai dần dần
  - Kiểm soát hành vi dựa trên ID Retailer

### 4. Ví dụ Luồng Sự kiện

1. **Sự kiện Thay đổi Tồn kho**:
   - Một giao dịch bán hàng xảy ra trong hệ thống POS
   - Hệ thống POS xuất bản event InventoryImpact đến Kafka

2. **Xử lý Event**:
   - Service theo dõi tiêu thụ event
   - Xác thực thông tin Retailer và sản phẩm
   - Xử lý tác động tồn kho

3. **Cập nhật Cơ sở dữ liệu**:
   - Tạo/cập nhật bản ghi theo dõi tồn kho
   - Tính toán lại bản ghi tồn kho bị ảnh hưởng
   - Cập nhật mức tồn kho hiện tại

4. **Xuất bản Thông báo**:
   - Xuất bản sự kiện thay đổi tồn kho cho hệ thống downstream
   - Gửi thông báo về thay đổi tồn kho

5. **Tiêu thụ Downstream**:
   - Hệ thống báo cáo tiêu thụ sự kiện để báo cáo
   - Service phân tích cập nhật bảng điều khiển
   - Các service kinh doanh khác phản ứng với thay đổi tồn kho

## Các Quy tắc Nghiệp vụ Chính

### Quy tắc Theo dõi Tồn kho

1. **Tính nhất quán theo Thứ tự thời gian**: 
   - Tất cả các chuyển động tồn kho được xử lý theo thứ tự thời gian nghiêm ngặt
   - Khi xảy ra xung đột, hệ thống sử dụng ID và mã chứng từ để giải quyết thứ tự

2. **Tính toán Giá vốn**:
   - Hỗ trợ phương pháp giá vốn trung bình và FIFO
   - Cập nhật giá vốn sản phẩm dựa trên cấu hình Retailer
   - Lan truyền thay đổi giá vốn qua lịch sử tồn kho

3. **Tồn kho Đặt trước**:
   - Theo dõi số lượng đặt trước riêng biệt cho sản phẩm
   - Cho phép đặt trước tồn kho mà không thay đổi giá trị tồn kho thực tế
   - Quan trọng cho tính toán sẵn có chính xác

4. **Sản phẩm Combo**:
   - Cập nhật tồn kho thành phần khi sản phẩm combo bị ảnh hưởng
   - Duy trì mối quan hệ công thức giữa combo và thành phần
   - Đảm bảo tồn kho nhất quán trong phân cấp sản phẩm

5. **Sản xuất**:
   - Theo dõi tiêu thụ nguyên liệu thô trong quá trình sản xuất
   - Cập nhật tồn kho thành phẩm
   - Duy trì mối quan hệ giá vốn giữa nguyên liệu và sản phẩm cuối cùng

### Xử lý Giao dịch

1. **Loại trừ Retailer**:
   - Hệ thống duy trì danh sách ID Retailer để loại trừ khỏi theo dõi tồn kho
   - Các Retailer này có thể được bỏ qua đối với các loại theo dõi cụ thể

2. **Hướng Tác động**:
   - Mỗi tác động có hướng advice: Add hoặc Remove
   - Add: tạo hoặc cập nhật bản ghi theo dõi
   - Remove: xóa bản ghi theo dõi

3. **Giới hạn Tác động**:
   - Hệ thống có giới hạn có thể cấu hình về khối lượng xử lý
   - Ngăn chặn vấn đề hiệu suất từ cập nhật hàng loạt

4. **Loại Giao dịch**:
   - Hỗ trợ cho các loại chứng từ khác nhau (hóa đơn, trả hàng, chuyển kho, v.v.)
   - Mỗi loại chứng từ tuân theo quy tắc xử lý tồn kho cụ thể

## Xử lý Lỗi

- **Xử lý Idempotent**: Event được xử lý với tính idempotent để ngăn chặn trùng lặp
- **Lọc Retailer**: Cấu hình tồn tại để bỏ qua các Retailer cụ thể
- **Phân loại Exception**: Các loại Exception khác nhau cho các điều kiện lỗi khác nhau
- **Giám sát**: Giám sát đặc biệt cho Retailer khối lượng cao

## Xem xét Kỹ thuật

- **Tối ưu hóa Hiệu suất**: Xử lý đặc biệt cho xử lý giao dịch khối lượng cao
- **Kiểm soát Đồng thời**: Đảm bảo xử lý chính xác trong các hoạt động đồng thời
- **Xử lý Giao dịch**: Sử dụng giao dịch cơ sở dữ liệu để duy trì tính nhất quán
- **Hỗ trợ Đa tiền tệ**: Xử lý các loại tiền tệ và chuyển đổi khác nhau cho Retailer

## Logic Nghiệp vụ Theo dõi Sản phẩm Chi tiết

### 1. Theo dõi Sản phẩm Tiêu chuẩn

#### Xử lý Tác động Tồn kho
- Sản phẩm được theo dõi riêng lẻ theo ProductId, RetailerId và BranchId
- Mỗi giao dịch tồn kho tạo một bản ghi theo dõi theo thứ tự thời gian
- Hệ thống duy trì bản ghi tồn kho lịch sử cũng như mức tồn kho hiện tại
- Giá vốn sản phẩm được tính toán và cập nhật dựa trên cấu hình Retailer
- Hệ thống thực thi giới hạn xử lý tối đa để ngăn chặn suy giảm hiệu suất

#### Quy tắc Xác thực
- Sản phẩm phải tồn tại trong hệ thống và là sản phẩm chính (không phải biến thể)
- Retailer phải hoạt động và được cấu hình để theo dõi
- Loại trừ có thể cấu hình cho phép bỏ qua theo dõi đối với Retailer cụ thể
- Ngày giao dịch được thực thi nghiêm ngặt để đảm bảo tính nhất quán theo thứ tự thời gian

#### Tính toán Mức Tồn kho
- Tồn kho hiện tại (OnHand) được tính toán dựa trên tất cả các giao dịch lịch sử
- Khi một giao dịch mới đến, tất cả các bản ghi sau đó được tính toán lại
- Hệ thống xử lý xung đột ngày giao dịch bằng cách thêm độ lệch micro giây
- Giá vốn được lan truyền từ giao dịch mua hàng để duy trì định giá chính xác

### 2. Theo dõi Sản phẩm Combo

#### Cấu trúc Sản phẩm Combo
- Một sản phẩm combo (bộ) bao gồm nhiều sản phẩm thành phần
- Mỗi thành phần có theo dõi tồn kho riêng nhưng được liên kết với combo cha
- Cấu trúc `ComboImpact` chứa:
  - ListComboTrackings: Bản ghi cho chính sản phẩm combo
  - ListMaterialTrackings: Bản ghi cho tất cả các nguyên liệu thành phần

#### Quản lý Quan hệ Thành phần
- Thành phần có mối quan hệ công thức với combo cha
- Khi combo được bán/trả lại, tồn kho của tất cả các thành phần bị ảnh hưởng theo tỷ lệ
- Hệ thống duy trì lịch sử công thức để theo dõi thay đổi trong thành phần combo

#### Xử lý Cấu trúc Cây
- Combo có thể có nhiều cấp độ (thuộc tính TreeLevel)
- Combo Cấp 1 là sản phẩm gốc với các thành phần trực tiếp
- Combo đa cấp (TreeLevel > 1) có các thành phần tự bản thân là combo
- Logic xử lý khác nhau được áp dụng dựa trên cấp độ cây:
  - Sản phẩm combo gốc sử dụng tra cứu lịch sử công thức trực tiếp
  - Thành phần cấp con cần duyệt cây để tìm mối quan hệ chính xác

#### Tính toán Giá vốn cho Combo
- Giá vốn sản phẩm combo là tổng giá vốn thành phần
- Khi giá vốn thành phần thay đổi, giá vốn combo được cập nhật tự động
- Hệ thống duy trì đồng bộ hóa giá vốn trong phân cấp sản phẩm
- Thay đổi công thức kích hoạt tính toán lại giá vốn trong toàn bộ cây bị ảnh hưởng

#### Xử lý Giao dịch Đặc biệt
- Khi bán/trả lại combo, hệ thống tạo theo dõi riêng biệt cho:
  1. Chính sản phẩm combo (theo dõi tồn kho, bán hàng)
  2. Mỗi nguyên liệu thành phần (giảm tồn kho thực tế)
- Các loại chứng từ khác nhau được hỗ trợ:
  - Giao dịch combo tiêu chuẩn (DocumentType.Invoice)
  - Giao dịch trả lại combo (DocumentType.RefundCombo)

#### Xác thực và Giới hạn
- Danh sách loại trừ Retailer có thể được cấu hình để bỏ qua theo dõi combo
- Số lượng xử lý tối đa có thể cấu hình để ngăn chặn vấn đề hiệu suất
- Lịch sử công thức được sử dụng để duy trì mối quan hệ chính xác ngay cả khi công thức thay đổi
- Thành phần bị thiếu hoặc mối quan hệ không hợp lệ được ghi nhật ký là thất bại

## Kết luận

Service Theo dõi Tồn kho là một thành phần quan trọng duy trì bản ghi tồn kho chính xác trong hệ thống bán lẻ KiotViet. Nó đảm bảo rằng tất cả các chuyển động tồn kho được ghi nhận đúng, mức tồn kho hiện tại chính xác và dữ liệu lịch sử được duy trì cho báo cáo và phân tích. Service tuân theo các quy tắc nghiệp vụ nghiêm ngặt để xử lý các kịch bản tồn kho khác nhau, từ chuyển động tồn kho đơn giản đến các hoạt động sản xuất và sản phẩm combo phức tạp.

Việc xử lý tinh vi của sản phẩm combo đảm bảo rằng theo dõi tồn kho duy trì tính toàn vẹn trong toàn bộ phân cấp sản phẩm, xử lý đúng cách tính toán giá vốn và điều chỉnh mức tồn kho ở tất cả các cấp của cấu trúc cây sản phẩm.