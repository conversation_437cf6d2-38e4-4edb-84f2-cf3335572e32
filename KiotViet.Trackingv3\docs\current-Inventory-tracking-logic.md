# Inventory Tracking Service Documentation

## Overview

The Inventory Tracking Service is responsible for tracking product inventory changes across the KiotViet retail system. It captures, processes, and maintains a comprehensive history of all inventory-related transactions, ensuring accurate stock levels and providing data for reporting and analysis.

## Core Components

### 1. Tracking Handlers

The system uses specialized handlers to process different types of inventory impacts:

#### InventoryTrackingHandler
- Processes inventory impacts related to product quantity changes
- Tracks actual physical stock movements
- Handles operations like purchases, sales, adjustments, transfers
- Maintains a history of all product quantity movements

#### StockTrackingHandler
- Processes stock impacts specifically related to reservation changes
- Manages reserved quantities separate from actual physical inventory
- Ensures accurate availability calculations (OnHand vs Reserved)

#### ComboTrackingHandler
- Specializes in processing combo/bundle product impacts
- Manages inventory tracking for products composed of multiple component items
- Ensures component inventory is updated when combo products change

#### ManufacturingTrackingHandler
- Handles inventory impacts from manufacturing processes
- Tracks raw material consumption and finished product production
- Manages inventory changes during assembly or manufacturing operations

### 2. Business Logic Components

#### TrackingHelper
The core implementation containing the primary inventory tracking business logic:

- **ProcessInventoryImpact**: Main method for processing inventory-related changes
  - Validates and processes inventory impact messages
  - Updates tracking records
  - Maintains inventory history for products
  - Updates current stock levels

- **AddOrUpdateTrackingAsync**: Creates or updates inventory tracking records
  - Handles document-based inventory changes
  - Calculates and maintains ending stock values
  - Updates cost information for products

- **UpdateInventorySinceAsync**: Recalculates inventory values for affected records
  - Updates all tracking records after the changed record
  - Ensures chronological consistency of inventory data
  - Handles time-based inventory conflicts

- **UpdateOnHandAsync**: Updates the current available quantity for products
  - Maintains the current quantity on hand in the inventory system
  - Updates cost and price information

- **DeleteTrackingAsync**: Removes inventory tracking records
  - Handles removal of erroneous or canceled inventory transactions
  - Ensures proper inventory recalculation after removal

#### Other Key Helpers
- **BalanceTrackingHelper**: Manages financial balance tracking related to inventory
- **PointTrackingHelper**: Tracks loyalty points connected to inventory transactions
- **ProductService**: Provides product data required for inventory processing

### 3. Data Flow

1. **Message Reception**: The system receives inventory impact messages from various sources
2. **Impact Processing**: These messages are processed by appropriate handlers
3. **Tracking Updates**: Records in the tracking system are created/updated
4. **Cascade Updates**: Related records affected by the change are updated
5. **Final Quantity Update**: The current OnHand and Reserved quantities are updated
6. **Notification**: Events are published for downstream systems

## Service Integration

### 1. Inputs to the Service

#### Message-Based Inputs
- **Kafka Messages**: The service consumes inventory impact messages from Kafka topics
  - StockImpact messages for reservation changes
  - InventoryImpact messages for physical inventory changes
  - ComboImpact messages for combo/bundle product changes
  - ManufacturingImpact messages for production processes

#### Configuration Inputs
- **AppSettings**: Configuration parameters for tracking behavior
- **Retailer Configurations**: Per-retailer settings for tracking behavior
- **Feature Toggles**: Unleash feature flags to control tracking functionality

### 2. Outputs from the Service

#### Database Updates
- **SQL Database Changes**: 
  - Updates to InventoryTracking tables
  - Changes to ProductBranch tables for current stock levels
  - Updates to cost information

#### MongoDB Documents
- **Tracking Objects**: Stores tracking history in MongoDB collections
  - InventoryTrackingObject
  - StockTrackingObject
  - ComboTrackingObject
  - ManufacturingTrackingObject

#### Published Events
- **Kafka Messages**: Publishes events for downstream consumption
- **RabbitMQ Notifications**: Sends notifications about inventory changes

### 3. Integration with Other Services

#### Database Integration
- **Shared SQL Database**: 
  - Reads from product and retailer tables
  - Updates inventory-related tables
  - Uses dynamic connection strings to access retailer-specific databases

- **MongoDB**: 
  - Stores tracking history and processed message IDs
  - Used for audit trails and tracking transaction history

#### Message-Based Integration

- **Kafka Integration**:
  - **Consumer**: Consumes inventory impact messages from upstream systems
    - Point-of-Sale systems
    - Order Management systems
    - Manufacturing modules
  - **Producer**: Publishes inventory update events for downstream systems
    - Reporting services
    - Analytics services
    - Notification services

- **RabbitMQ Integration**:
  - Sends notifications about inventory changes
  - Communicates with other microservices in the ecosystem

#### Service-to-Service Integration

- **Redis Cache**: 
  - Caches product and retailer information
  - Improves performance by reducing database queries

- **Unleash Service**:
  - Feature toggling for gradual rollout of functionality
  - Controls behavior based on retailer IDs

### 4. Event Flow Example

1. **Inventory Change Event**:
   - A sale occurs in the POS system
   - POS system publishes an InventoryImpact message to Kafka

2. **Message Processing**:
   - Tracking service consumes the message
   - Validates retailer and product information
   - Processes the inventory impact

3. **Database Updates**:
   - Creates/updates inventory tracking records
   - Recalculates affected inventory records
   - Updates current stock levels

4. **Notification Publishing**:
   - Publishes inventory change events for downstream systems
   - Sends notifications about inventory changes

5. **Downstream Consumption**:
   - Reporting systems consume events for reporting
   - Analytics services update dashboards
   - Other business services react to inventory changes

## Key Business Rules

### Inventory Tracking Rules

1. **Chronological Consistency**: 
   - All inventory movements are processed in strict chronological order
   - When conflicts occur, the system uses document IDs and codes to resolve ordering

2. **Cost Calculation**:
   - Supports average cost and FIFO cost methods
   - Updates product costs based on retailer configuration
   - Propagates cost changes through inventory history

3. **Reserved Stock**:
   - Tracks separate reservation quantities for products
   - Allows for reserving inventory without changing physical on-hand values
   - Critical for accurate availability calculations

4. **Combo Products**:
   - Updates component inventory when combo products are affected
   - Maintains formula relationships between combos and components
   - Ensures consistent inventory across product hierarchies

5. **Manufacturing**:
   - Tracks raw material consumption during manufacturing
   - Updates finished product inventory
   - Maintains cost relationships between materials and final products

### Transaction Processing

1. **Retailer Exclusions**:
   - System maintains a list of retailer IDs to exclude from inventory tracking
   - These retailers can be ignored for specific types of tracking

2. **Impact Direction**:
   - Each impact has an advice direction: Add or Remove
   - Add: creates or updates tracking records
   - Remove: deletes tracking records

3. **Impact Limitation**:
   - System has configurable limits on processing volume
   - Prevents performance issues from mass updates

4. **Transaction Types**:
   - Support for various document types (invoices, returns, transfers, etc.)
   - Each document type follows specific inventory handling rules

## Error Handling

- **Idempotent Processing**: Messages are processed with idempotency to prevent duplicates
- **Retailer Filtering**: Configuration exists to ignore specific retailers
- **Exception Categorization**: Different exception types for various error conditions
- **Monitoring**: Special monitoring for high-volume retailers

## Technical Considerations

- **Performance Optimization**: Special handling for high-volume transaction processing
- **Concurrency Control**: Ensures accurate processing during concurrent operations
- **Transaction Handling**: Uses database transactions to maintain consistency
- **Multi-Currency Support**: Handles different currencies and conversion for retailers

## Detailed Product Tracking Business Logic

### 1. Standard Product Tracking

#### Inventory Impact Processing
- Products are tracked individually by ProductId, RetailerId, and BranchId
- Each inventory transaction creates a tracking record in chronological order
- The system maintains historical inventory records as well as current stock levels
- Product cost is calculated and updated based on retailer configuration
- The system enforces maximum processing limits to prevent performance degradation

#### Validation Rules
- Products must exist in the system and be master products (not variants)
- Retailers must be active and configured for tracking
- Configurable exclusions allow for skipping tracking for specific retailers
- Transaction dates are strictly enforced for chronological consistency

#### Stock Level Calculations
- Current stock (OnHand) is calculated based on all historical transactions
- When a new transaction arrives, all subsequent records are recalculated
- The system handles transaction date conflicts by adding microsecond offsets
- Cost is propagated from purchase transactions to maintain accurate valuations

### 2. Combo Product Tracking

#### Combo Product Structure
- A combo product (bundle) consists of multiple component products
- Each component has its own inventory tracking but is linked to the parent combo
- The `ComboImpact` structure contains:
  - ListComboTrackings: Records for the combo product itself
  - ListMaterialTrackings: Records for all component materials

#### Component Relationship Management
- Components have a formula relationship with parent combos
- When a combo is sold/refunded, all components' inventory is affected proportionally
- The system maintains formula history to track changes in combo composition

#### Tree Structure Processing
- Combos can have multiple levels (TreeLevel property)
- Level 1 combos are root products with direct components
- Multi-level combos (TreeLevel > 1) have components that are themselves combos
- Different processing logic is applied based on tree level:
  - Root combo products use direct formula history lookup
  - Sub-level components need tree traversal to find the correct relationships

#### Cost Calculation for Combos
- Combo product cost is the sum of its component costs
- When component costs change, combo costs are updated automatically
- The system maintains cost synchronization across the product hierarchy
- Formula changes trigger cost recalculations throughout the affected tree

#### Special Transaction Handling
- When selling/refunding combos, the system creates separate tracking for:
  1. The combo product itself (inventory, sales tracking)
  2. Each component material (actual stock reduction)
- Different document types are supported:
  - Standard combo transactions (DocumentType.Invoice)
  - Refund combo transactions (DocumentType.RefundCombo)

#### Validation and Limitations
- Retailer exclusion lists can be configured to skip combo tracking
- Maximum processing items are configurable to prevent performance issues
- Formula history is used to maintain accurate relationships even if formulas change
- Missing components or invalid relationships are logged as failures

## Conclusion

The Inventory Tracking Service is a critical component that maintains accurate inventory records across the KiotViet retail system. It ensures that all inventory movements are properly recorded, current stock levels are accurate, and historical data is maintained for reporting and analysis. The service follows strict business rules to handle various inventory scenarios, from simple stock movements to complex manufacturing and combo product operations. 

The sophisticated handling of combo products ensures that inventory tracking maintains integrity throughout product hierarchies, properly handling cost calculations and stock level adjustments at all levels of the product tree structure.