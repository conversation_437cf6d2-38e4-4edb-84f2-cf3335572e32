﻿using Confluent.Kafka;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.AutoCustomerGroup.Services.Model;
using ServiceStack;
using System;
using System.Threading.Tasks;

namespace KiotViet.AutoCustomerGroup.Services.Impl
{
    public class CustomerBulkChangeInfoHandler : ICustomerGroupHandler<CustomerBulkChangeInfo>
    {
        private readonly IAutoCgService _AutocgService;
        public CustomerBulkChangeInfoHandler(IAutoCgService autoCgService)
        {
            _AutocgService = autoCgService ?? throw new ArgumentNullException(nameof(autoCgService));
        }
        public async Task ProcessMessage(ConsumeResult<string,string> message)
        {
            var customerInfo = message.Value.FromJson<CustomerBulkChangeInfo>();
            await _AutocgService.BulkProcessCustomerInfoChange(customerInfo);
        }
    }
}
