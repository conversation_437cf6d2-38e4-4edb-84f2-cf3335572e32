﻿using System.Collections.Generic;

namespace KiotViet.Data.ElasticSearch
{
    public class ProductSerialsAddModel
    {
        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        public IEnumerable<string> Serials { get; set; }
        /// <summary>
        /// List of product ids
        /// </summary>
        public IEnumerable<long> Ids { get; set; }
        public int Zone { get; set; }
        public string ConnectionString { get; set; }
    }
}