﻿using System;
using KiotViet.MongoDb.Interface;
using KiotViet.Services.Common;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class TrackingTransactionObject :  TrackingTransaction, IEntityId, ICreatedOn, IModifiedDate
    {
        //db.TrackingTransactionObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.TrackingTransactionObject.createIndex( { "GuidKey": 1 })
        //db.TrackingTransactionObject.createIndex( { "Track.RetailerId": 1, "Track.DocumentId": 1, "Track.DocumentType": 1, "Track.ProductId": 1  })
        //db.TrackingTransactionObject.createIndex( { "ListImpacts.RetailerId": 1, "ListImpacts.BranchId": 1, "ListImpacts.ProductId": 1 })
        public DateTime CreatedAt { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public ObjectId _id { get; set; }
        
        public void Copy(TrackingTransaction trackingTransaction)
        {
            if (trackingTransaction == null) return;
            Topic = trackingTransaction.Topic;
            PartitionKey = trackingTransaction.PartitionKey;
            GuidKey = trackingTransaction.GuidKey;
            ListImpacts = trackingTransaction.ListImpacts;
        }
    }

    //db.TrackingTransactionBeforeObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
    //db.TrackingTransactionBeforeObject.createIndex( { "GuidKey": 1 })
    //db.TrackingTransactionBeforeObject.createIndex( { "Track.RetailerId": 1, "Track.DocumentId": 1, "Track.DocumentType": 1, "Track.ProductId": 1  })
    //db.TrackingTransactionBeforeObject.createIndex( { "ListImpacts.RetailerId": 1, "ListImpacts.BranchId": 1, "ListImpacts.ProductId": 1 })
    public class TrackingTransactionBeforeObject : TrackingTransactionObject { }
}