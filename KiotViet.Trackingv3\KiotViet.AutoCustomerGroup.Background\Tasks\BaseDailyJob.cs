﻿using System;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.Util;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace KiotViet.AutoCustomerGroup.Background.Tasks
{
    public abstract class BaseDailyJob<T> : IHostedService where T : class
    {
        private Timer _timer;
        private readonly CancellationTokenSource _stoppingCts = new CancellationTokenSource();
        protected ILogger<T> Logger { get; }

        protected BaseDailyJob(ILogger<T> logger)
        {
            Logger = logger;
        }

        protected abstract string TimeExecuteConfigKey { get; set; }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            var executeAt = TimeSpan.Parse(KvAppConfig.KvTrackingConfigurationBuilder[TimeExecuteConfigKey]);

            var lastRun = DateTime.Now;
            _stoppingCts.Token.ThrowIfCancellationRequested();
            _timer = new Timer(async x =>
            {
                var currentTime = DateTime.Now;
                if (currentTime.Date <= lastRun.Date || currentTime.TimeOfDay < executeAt)
                {
                    return;
                }

                lastRun = currentTime;
                await ExecuteJob(currentTime);
            }, cancellationToken, TimeSpan.Zero, TimeSpan.FromMinutes(10));
            Logger.LogInformation($"Daily job scheduled at: {executeAt}");

            if (_stoppingCts.Token.IsCancellationRequested)
            {
                _stoppingCts.Token.ThrowIfCancellationRequested();
            }
            return Task.CompletedTask;
        }

        protected abstract Task ExecuteJob(DateTime time);

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _timer.Dispose();
            return Task.CompletedTask;
        }


    }
}