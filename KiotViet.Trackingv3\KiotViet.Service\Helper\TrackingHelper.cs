using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Net.Sockets;
using System.Threading;
using System.Threading.Tasks;
using Confluent.Kafka;
using KiotViet.Data.Entity;
using KiotViet.Data.Interface;
using KiotViet.Data.Notification;
using KiotViet.MongoDb.Entity;
using KiotViet.Redis.Entity;
using KiotViet.Repository.Impl;
using KiotViet.Service.Configs;
using KiotViet.Service.Constants;
using KiotViet.Service.Interface;
using KiotViet.Services.Common;
using KiotViet.Util;
using KiotVietFnB.Abstractions.Caching;
using Microsoft.Extensions.Logging;
using ServiceStack;
using ServiceStack.Data;
using ServiceStack.OrmLite;
using static KiotViet.Util.BunchOfEnum;

namespace KiotViet.Service.Helper
{
    public class TrackingHelper : ITrackingHelper
    {
        private const string UsingInventoryTrackingV4 = "UsingInventoryTrackingV4";
        private const string TrackingProductCacheKey = "TrackingService:Product:RetailerId_{0}:ProductId_{1}";
        private const string TrackingBranchCacheKey = "TrackingService:Branch:RetailerId_{0}:BranchId_{1}";

        private readonly IDbConnectionFactory _connectionFactory;
        private readonly IProductService _productService;
        private readonly ITrackingKafkaProducer _trackingKafkaProducer;
        private readonly ITrackingNotiRabbitProducer _trackingNotiRabbitProducer;
        private readonly ITrackingNotiRabbitConfiguration _trackingNotiRabbitConfiguration;
        private readonly ILogger<TrackingHelper> _logger;
        public readonly IBalanceTrackingHelper _balanceTrackingHelper;
        public readonly IPointTrackingHelper _pointTrackingHelper;
        public readonly IMultiCurrencyService _multiCurrencyService;
        private readonly Func<decimal, decimal> DefaultRoundFunction = x => ConvertHelper.RoundDecimal(x);
        private readonly ICacheClient _cacheClient;


        public readonly IUnleashService _unleashService;

#pragma warning disable S107
        public TrackingHelper(
            IDbConnectionFactory connectionFactory,
            IProductService productService,
            ITrackingKafkaProducer trackingKafkaProducer,
            ITrackingNotiRabbitProducer trackingNotiRabbitProducer,
            ITrackingNotiRabbitConfiguration trackingNotiRabbitConfiguration,
            IBalanceTrackingHelper balanceTrackingHelper,
            IPointTrackingHelper pointTrackingHelper,
            ILogger<TrackingHelper> logger,
            IMultiCurrencyService multiCurrencyService,
            IUnleashService unleashService,
            ICacheClient cacheClient)
        {
            _connectionFactory = connectionFactory;
            _productService = productService;
            _trackingKafkaProducer = trackingKafkaProducer;
            _trackingNotiRabbitProducer = trackingNotiRabbitProducer;
            _trackingNotiRabbitConfiguration = trackingNotiRabbitConfiguration;
            _logger = logger;
            _balanceTrackingHelper = balanceTrackingHelper;
            _pointTrackingHelper = pointTrackingHelper;
            _multiCurrencyService = multiCurrencyService;
            _unleashService = unleashService;
            _cacheClient = cacheClient;
        }
#pragma warning restore S107

        #region helper private

        private static void UpdateEndingStock(InventoryImpact impact, InventoryTracking before)
        {

            // Force EndingStocks to 0 for Customizable Combo parent before saving
            if (impact.ProductCache?.ProductType == (int)ProductType.Customizable)
            {
                impact.Track.EndingStocks = 0;
                return;
            }

            if (impact.Track.DocumentType == (int)BunchOfEnum.DocumentType.StockTake)
                impact.Track.Quantity = impact.Track.EndingStocks - (before?.EndingStocks ?? 0);
            else
                impact.Track.EndingStocks = (before?.EndingStocks ?? 0) + impact.Track.Quantity;
        }

        private static void UpdateEndingStock(InventoryTracking tracking, InventoryTracking before)
        {
            if (tracking.DocumentType == (int)BunchOfEnum.DocumentType.StockTake)
                tracking.Quantity = tracking.EndingStocks - (before?.EndingStocks ?? 0);
            else
                tracking.EndingStocks = (before?.EndingStocks ?? 0) + tracking.Quantity;
        }



        private static InventoryTracking GetDummyTracking(long productId, int retailerId, int branchId)
        {
            var dummy = new DummyInventoryTracking
            {
                ProductId = productId,
                RetailerId = retailerId,
                BranchId = branchId,
                TransDate = new DateTime(1900, 1, 1)
            };
            return dummy;
        }

        private async Task UpdateLatestCostCombo(IDbConnection db, string conn, int retailerId, int branchId, long productId, CancellationToken cancellationToken = default)
        {
            if (db != null && retailerId > 0 && productId > 0)
            {
                var pbRepository = new ProductBranchRepository(db, _logger);
                // find current 
                var materials = await pbRepository.GetLastestMaterialsByProductId(retailerId, productId);
                if (materials != null && materials.Count > 0)
                {
                    decimal cost = 0;
                    foreach (var material in materials)
                    {
                        cost += await SetNodeValues(conn, retailerId, branchId, material);
                    }
                    var productBranch = await pbRepository.GetProductBranchAsync(productId, branchId, retailerId);
                    productBranch.Cost = cost;
                    await pbRepository.Update(productBranch);
                }
            }
        }

        private async Task<decimal> SetNodeValues(string conn, int retailerId, int branchId, ProductFormulaHistoryItem material, CancellationToken cancellationToken = default)
        {
            if (material == null) return 0;
            decimal cost = 0;
            if (material.ListMaterials == null || !material.ListMaterials.Any() || (material.ListMaterials.Any() && material.ProductType != (int)BunchOfEnum.ProductType.Manufactured))
            {
                //If it has no material, it is a leaf. calculate cost it self   
                // calculate itself
                //If it has no material, it is a leaf. calculate cost it self              
                using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(conn)))
                {
                    var pbRepository = new ProductBranchRepository(db, _logger);
                    var materilItk = await pbRepository.GetProductBranchByPrimaryKeyAsync(material.MaterialId, branchId, retailerId);
                    cost = materilItk?.Cost * (decimal)material.Quantity ?? 0;
                }
                return cost;
            }
            else
            {
                //Make sure all child-nodes have values
                foreach (var childNode in material.ListMaterials)
                {
                    cost += await SetNodeValues(conn, retailerId, branchId, childNode);
                }
                cost *= (decimal)material.Quantity;
            }
            return cost;
        }

        private async Task UpdateCost(IDbConnection db, string conn, bool isAvg, InventoryTracking current,
            InventoryTracking before, Product productCache, Return refundRetrieve, InventoryTracking invoiceRetrieve, CancellationToken cancellationToken = default)
        {
            var itkRepository = new InventoryTrackingRepository(db, _logger);
            var returnRepository = new ReturnRepository(db);

            #region Avg and Refund
            if (isAvg && (current.DocumentType == (int)BunchOfEnum.DocumentType.Refund 
            || current.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo
            || current.DocumentType == (int)BunchOfEnum.DocumentType.RefundCustomizebleCombo))
            {
                var isFound = false;
                var returnDoc = refundRetrieve != null && refundRetrieve.IsFound
                    ? refundRetrieve
                    : await returnRepository.GetByIdAsync(current.DocumentId);
                if (returnDoc?.InvoiceId != null)
                {
                    if (before != null && before.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn &&
                        before.EndingStocks >= 0 && (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                         before.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
                    {
                        if (returnDoc.IsHaveTracking)
                        { 
                            isFound = true;
                            current.Cost = returnDoc.InvoiceCost;
                            CalculateCostWhenPurchaseReturn(true, current, before);
                        }
                        else
                        {
                            var inventoryTracking = await itkRepository.GetByPrimaryKey(current.ProductId,
                                returnDoc.InvoiceId ?? 0,
                                GetDocumentTypeForRefund(current));
                            if (inventoryTracking != null)
                            {
                                isFound = true;
                                current.Cost = inventoryTracking.Cost;
                                CalculateCostWhenPurchaseReturn(true, current, before);
                            }
                        }
                    }
                    else
                    {
                        if (returnDoc.IsHaveTracking)
                        {
                            isFound = true;
                            //giá vốn tại thời điểm bán
                            if (before != null && before.EndingStocks > 0)
                            {
                                current.Cost = (before.Cost * (decimal)before.EndingStocks + returnDoc.InvoiceCost * (decimal)current.Quantity) / (decimal)(before.EndingStocks + current.Quantity);
                            }
                            else
                            {
                                if (before != null && before.IsSpecial)
                                {
                                    current.Cost = (before.HiddenCost + returnDoc.InvoiceCost * (decimal)current.Quantity) / (decimal)current.Quantity;
                                }
                                else
                                {
                                    current.Cost = returnDoc.InvoiceCost;
                                }
                            }
                        }
                        else
                        {
                            var inventoryTracking = await itkRepository.GetByPrimaryKey(current.ProductId, returnDoc.InvoiceId ?? 0,
                                current.DocumentType == (int)BunchOfEnum.DocumentType.Refund ? BunchOfEnum.DocumentType.Invoice : BunchOfEnum.DocumentType.Combo);
                            if (inventoryTracking != null)
                            {
                                isFound = true;
                                //giá vốn tại thời điểm bán
                                if (before != null && before.EndingStocks > 0)
                                {
                                    current.Cost = (before.Cost * (decimal)before.EndingStocks + inventoryTracking.Cost * (decimal)current.Quantity) / (decimal)(before.EndingStocks + current.Quantity);
                                }
                                else
                                {
                                    if (before != null && before.IsSpecial)
                                    {
                                        current.Cost = (before.HiddenCost + inventoryTracking.Cost * (decimal)current.Quantity) / (decimal)current.Quantity;
                                    }
                                    else
                                    {
                                        current.Cost = inventoryTracking.Cost;
                                    }
                                }
                            }
                        }
                    }
                }
                if (!isFound)
                {
                    // gia von cua tracking gan nhat
                    if (before != null)
                    {
                        if (before.IsSpecial)
                        {
                            current.Cost = (before.HiddenCost + before.Cost * (decimal)current.Quantity) / (decimal)current.Quantity;
                        }
                        else
                        {
                            current.Cost = before.Cost;
                        }
                    }
                    else
                    {
                        current.Cost = 0;
                    }
                }
            }
            #endregion

            #region Avg and DeliveryFailed
            else if (isAvg && (current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed 
            || current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo 
            || current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCustomizebleCombo))
            {
                var inventoryTracking = invoiceRetrieve ?? await itkRepository.GetByPrimaryKey(current.ProductId, current.DocumentId
                    , GetDocumentTypeForDeliveryFailed(current));
                if (inventoryTracking != null)
                {
                    if (before != null && before.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn &&
                        before.EndingStocks >= 0 && (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                         before.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
                    {
                        current.Cost = inventoryTracking.Cost;
                        CalculateCostWhenPurchaseReturn(true, current, before);
                    }
                    else
                    {
                        //giá vốn tại thời điểm bán
                        if (before != null && before.EndingStocks > 0)
                        {
                            current.Cost = (before.Cost * (decimal)before.EndingStocks + inventoryTracking.Cost * (decimal)current.Quantity) / (decimal)(before.EndingStocks + current.Quantity);
                        }
                        else
                        {
                            if (before != null && before.IsSpecial)
                            {
                                current.Cost = (before.HiddenCost + inventoryTracking.Cost * (decimal)current.Quantity) / (decimal)current.Quantity;
                            }
                            else
                            {
                                current.Cost = inventoryTracking.Cost;
                            }
                        }
                    }
                }
                else
                {
                    current.Cost = before?.Cost ?? 0;
                }
            }
            #endregion

            #region Avg and Manufacturing
            else if (isAvg && current.DocumentType == (int)BunchOfEnum.DocumentType.Manufacturing)
            {
                // get all cost of materials
                var allMaterials = await itkRepository.GetLatestCostMaterialsByDocId(current.RetailerId, current.BranchId, current.DocumentId, current.TransDate);
                var costCalculate = allMaterials.Sum(x => x.Cost * (decimal)x.Quantity);
                current.Price = costCalculate / (decimal)current.Quantity;
                if (before != null)
                {
                    if ((decimal)before.EndingStocks < 0)
                    {
                        if (before.IsSpecial)
                        {
                            current.Cost = (before.HiddenCost + costCalculate) / (decimal)current.Quantity;
                        }
                        else
                        {
                            current.Cost = costCalculate / (decimal)current.Quantity;
                        }
                    }
                    else
                    {
                        // calculate with value below
                        if (before.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn && before.EndingStocks >= 0 &&
                            (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                             before.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
                        {
                            CalculateCostWhenPurchaseReturn(true, current, before);
                        }
                        else
                        {
                            if (Math.Abs(before.EndingStocks + current.Quantity) > KvStatic.Tolerance)
                            {
                                current.Cost = (before.Cost * (decimal)before.EndingStocks + costCalculate) / (decimal)(before.EndingStocks + current.Quantity);
                            }
                            else
                            {
                                if (before.IsSpecial)
                                {
                                    current.Cost = (before.HiddenCost + costCalculate * (decimal)current.Quantity) / (decimal)current.Quantity;
                                }
                                else
                                {
                                    current.Cost = costCalculate / (decimal)current.Quantity;
                                }
                            }
                        }
                    }
                }
                else
                {
                    current.Cost = costCalculate / (decimal)current.Quantity;
                }
            }
            #endregion

            #region Avg and PurchaseReturn
            else if (isAvg && current.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn &&
                     (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                      current.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
            {
                double beforeEnding = 0;
                decimal beforeCost = 0;
                if (before != null)
                {
                    beforeEnding = before.EndingStocks;
                    beforeCost = before.Cost;
                }
                current.Cost = beforeCost;
                var lastEndind = beforeEnding + current.Quantity;
                // lastEndind < 0
                if (lastEndind < 0)
                {
                    current.Cost = beforeCost;
                    if (before != null && before.IsSpecial)
                    {
                        current.HiddenCost = before.HiddenCost;
                        current.IsSpecial = true;
                    }
                    else
                    {
                        current.HiddenCost = 0;
                        current.IsSpecial = false;
                    }
                }
                // lastEndind = 0
                // 8204 addition IsSpecial, replace Cost by HiddenCost 
                else if (Math.Abs(lastEndind) < KvStatic.Tolerance)
                {
                    current.HiddenCost = beforeCost * (decimal)beforeEnding - current.Price * (decimal)Math.Abs(current.Quantity);
                    current.IsSpecial = true;
                }
                //lastEndind > 0
                else
                {
                    // update 9697 KiotViet space
                    if (lastEndind < 1)
                    {
                        // update 11873
                        current.HiddenCost = beforeCost * (decimal)beforeEnding - current.Price * (decimal)Math.Abs(current.Quantity);
                        current.IsSpecial = true;
                    }
                    else
                    {
                        current.Cost = (beforeCost * (decimal)beforeEnding - current.Price * (decimal)Math.Abs(current.Quantity)) / (decimal)(beforeEnding + current.Quantity);
                        current.HiddenCost = 0;
                        current.IsSpecial = false;
                    }
                }
            }
            #endregion

            #region Another case
            else
            {
                if (!isAvg)
                {
                    if (current.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost)
                    {
                        current.Cost = current.Price;
                    }
                    else
                    {
                        var pbRepository = new ProductBranchRepository(db, _logger);
                        var currentCost = await pbRepository.GetProductBranchAsync(current.ProductId, current.BranchId, current.RetailerId);
                        if ((currentCost == null || currentCost.Cost == 0)
                            && (current.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseInvoice || current.DocumentType == (int)BunchOfEnum.DocumentType.Receive)
                            && current.Price > 0)
                        {
                            current.Cost = current.Price;
                        }
                        else
                        {
                            // update for 7793 KiotViet space
                            // update cost = 0, and retry wil help cost of material equals combo
                            if (before == null || before is DummyInventoryTracking)
                            {
                                // update for 8908 KiotViet space
                                if (current.DocumentType == (int)BunchOfEnum.DocumentType.Manufacturing)
                                {
                                    // get all cost of materials
                                    var allMaterials = await itkRepository.GetLatestCostMaterialsByDocId(current.RetailerId, current.BranchId, current.DocumentId, current.TransDate);
                                    var costCalculate = allMaterials.Sum(x => x.Cost * (decimal)x.Quantity) / (decimal)current.Quantity;
                                    if (costCalculate > 0)
                                    {
                                        current.Price = costCalculate;
                                        current.Cost = costCalculate;
                                    }
                                }
                                else
                                {
                                    // update for 5524 kiotviet customer space
                                    // if before null or dummy, but currentCost > 0, and current tracking is PurchaseInvoice or Receive, then pass cost = price
                                    if ((current.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseInvoice || current.DocumentType == (int)BunchOfEnum.DocumentType.Receive) && current.Price > 0)
                                    {
                                        current.Cost = current.Price;
                                    }
                                    else if (current.ProductType != (int)BunchOfEnum.ProductType.Customizable)
                                    {
                                        current.Cost = 0;
                                    }
                                }
                            }
                            else
                            {
                                // Handle cost calculation based on document type
                                switch (current.DocumentType)
                                {
                                    case (int)BunchOfEnum.DocumentType.Combo:
                                    case (int)BunchOfEnum.DocumentType.CustomizebleCombo:
                                        var (isHaveAnyValid, cost) = await itkRepository.GetLatestAvailableFixedPrice(
                                            current.RetailerId, 
                                            current.BranchId,
                                            current.ProductId,
                                            current.TransDate
                                        );
                                        current.Cost = isHaveAnyValid ? (cost ?? 0) : (currentCost?.Cost ?? 0);
                                        break;
                                    default:
                                        if (current.ProductType != (int)BunchOfEnum.ProductType.Customizable)
                                        {
                                            current.Cost = currentCost?.Cost ?? 0;
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }
                else
                {
                    if (productCache.Formula != null && productCache.Formula.Any()
                                                     && productCache.ProductType != (int)BunchOfEnum.ProductType.Manufactured
                                                     && current.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost)
                    {
                        // fix 9110, support 7609, need check have any trans unless stocktake and changeCost
                        var isHaveAnyTransUnlessStkChg = await itkRepository.IsHaveAnyUnlessStkChgAsync(current);
                        if (isHaveAnyTransUnlessStkChg)
                        {
                            // keep avg cost logic
                            await UpdateAvgCost(conn, current, before);
                        }
                        else
                        {
                            await UpdateAvgCost(conn, current, null);
                        }
                    }
                    else
                    {
                        if (current.ProductType != (int) ProductType.Customizable) // Customizeble combo parent not effected by avg cost.
                        {
                            await UpdateAvgCost(conn, current, before);
                        }
                    }
                }
            }
            #endregion

            // retry to get cost if 
            // after cost is 0
            // after is (-) doctype or refund
            // before is dummy
            await TryResolveCost(db, current, before, productCache, cancellationToken);
        }

        private static DocumentType GetDocumentTypeForRefund(InventoryTracking current)
        {
            if (current.DocumentType == (int)BunchOfEnum.DocumentType.Refund)
                return BunchOfEnum.DocumentType.Invoice;
            if (current.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo) 
                return BunchOfEnum.DocumentType.Combo;
            return BunchOfEnum.DocumentType.CustomizebleCombo;
        }

        private static DocumentType GetDocumentTypeForDeliveryFailed(InventoryTracking current)
        {
            if (current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed)
                return BunchOfEnum.DocumentType.Invoice;
            if (current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo)
                return BunchOfEnum.DocumentType.Combo;
            return BunchOfEnum.DocumentType.CustomizebleCombo;
        }

        private async Task TryResolveCost(IDbConnection db, InventoryTracking current, InventoryTracking before, Product productCache, CancellationToken cancellationToken = default)
        {
            var itkRepository = new InventoryTrackingRepository(db, _logger);
            if (current.Cost.Equals(0) &&
                (current.DocumentType == (int)BunchOfEnum.DocumentType.Invoice ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.StockTake ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.Tranfer ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.Combo ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.CustomizebleCombo ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCustomizebleCombo ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.ManufacturingMaterial ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.DamageItem ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.Refund ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost ||
                 current.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn) &&
                (before is DummyInventoryTracking || before == null))
            {
                // find nearest in CB, PN, Transfer > TransDate and price != 0
                var lastPrice = await itkRepository.GetLatestAvailablePrice(current.RetailerId, current.BranchId, current.ProductId, current.TransDate);
                if (lastPrice != null && lastPrice != 0)
                {
                    current.Cost = lastPrice.Value;
                }
                // if can't find
                // if materials > 0 && Purchased Product  => current cost in all of materials, if still = 0, then try to get current cost in ProductBranch
                // if materials = 0 => current cost in ProductBranch
                else
                {
                    if (productCache != null && productCache.Formula != null && productCache.Formula.Count > 0 && productCache.ProductType.Equals((int)BunchOfEnum.ProductType.Purchased))
                    {
                        var lastCostAllMaterials = await itkRepository.GetLatestCostAllMaterials(current.ProductId, productCache.Formula.Select(x => x.Id), current.TransDate, current.RetailerId, current.BranchId);
                        if (lastCostAllMaterials != null)
                        {
                            current.Cost = lastCostAllMaterials.Value;
                        }
                    }
                    else
                    {
                        var pbRepository = new ProductBranchRepository(db, _logger);
                        var pbLastest = await pbRepository.GetProductBranchByPrimaryKeyAsync(current.ProductId, current.BranchId, current.RetailerId);
                        if (pbLastest != null)
                        {
                            current.Cost = pbLastest.Cost;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// only use this function when before.doctype equal purchaseReturn and before.endingsStocks &gt;= 0
        /// </summary>
        /// <param name="isAvg"></param>
        /// <param name="current"></param>
        /// <param name="before"></param>
        private static void CalculateCostWhenPurchaseReturn(bool isAvg, InventoryTracking current, InventoryTracking before)
        {
            if (!isAvg) return;
            if (before == null || current == null) return;
            var currentPrice = current.Price;
            if (current.DocumentType == (int)BunchOfEnum.DocumentType.Refund ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.RefundCustomizebleCombo ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCustomizebleCombo)
            {
                currentPrice = current.Cost;
            }
            if (current.Quantity <= 0)
            {
                current.Cost = before.Cost;
                if (before.IsSpecial)
                {
                    current.IsSpecial = true;
                    current.HiddenCost = before.HiddenCost;
                }
                else
                {
                    current.IsSpecial = false;
                    current.HiddenCost = 0;
                }
            }
            else
            {
                // update #8204 EndingStocks = 0
                if (Math.Abs(before.EndingStocks) < KvStatic.Tolerance)
                {
                    // apply for new data
                    if (before.IsSpecial)
                    {
                        current.Cost = (before.HiddenCost + currentPrice * (decimal)current.Quantity) / (decimal)current.Quantity;
                        current.IsSpecial = false;
                        current.HiddenCost = 0;
                    }
                    // apply for old data, before #8204
                    else
                    {
                        current.Cost = (before.Cost + currentPrice * (decimal)current.Quantity) / (decimal)current.Quantity;
                    }
                }
                if (before.EndingStocks > 0)
                {
                    // update 11873
                    if (before.EndingStocks < 1 && before.IsSpecial)
                    {
                        current.Cost = (before.HiddenCost + currentPrice * (decimal)current.Quantity) / (decimal)(current.Quantity + before.EndingStocks);
                    }
                    else
                    {
                        current.Cost = (before.Cost * (decimal)before.EndingStocks + currentPrice * (decimal)current.Quantity) / (decimal)(current.Quantity + before.EndingStocks);
                    }
                }
            }
        }

        private async Task<bool> IsHaveAnyPurchaseBeforeTpl(string connection, InventoryTracking tracking, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(connection)))
            {
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                return await itkRepository.IsHaveAnyPurchaseBeforeAsync(tracking);
            }
        }

        private async Task<decimal?> GetCostAdjustmentById(string connection, long docId, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(connection)))
            {
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                return await itkRepository.GetCostAdjustmentById(docId);
            }
        }

        private async Task UpdateAvgCost(string connection, InventoryTracking current, InventoryTracking before, CancellationToken cancellationToken = default)
        {
            if (current.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseInvoice ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.Receive ||
                current.DocumentType == (int)BunchOfEnum.DocumentType.Refund)
            {
                if (before != null && before.EndingStocks > 0)
                {
                    if (before.EndingStocks < 1 &&
                        before.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn &&
                        (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                         before.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
                    {
                        CalculateCostWhenPurchaseReturn(true, current, before);
                    }
                    else
                    {
                        current.Cost = (before.Cost * (decimal)before.EndingStocks + current.Price * (decimal)current.Quantity) / (decimal)(before.EndingStocks + current.Quantity);
                    }
                }
                else
                {
                    if (before != null && before.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn &&
                        before.EndingStocks >= 0 && (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                         before.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
                    {
                        CalculateCostWhenPurchaseReturn(true, current, before);
                    }
                    else
                    {
                        // #2610 when before.Cost == 0 && before.EndingStocks > 0
                        if (before != null && before.EndingStocks > 0)
                        {
                            var isHaveAnyPurchase = await IsHaveAnyPurchaseBeforeTpl(connection, current);
                            if (isHaveAnyPurchase)
                            {
                                var all = current.Quantity + (before.EndingStocks);
                                if (all > KvStatic.Tolerance)
                                {
                                    current.Cost = (current.Price * (decimal)current.Quantity) / (decimal)all;
                                }
                                else
                                {
                                    current.Cost = current.Price;
                                }
                            }
                            else
                            {
                                current.Cost = current.Price;
                            }
                        }
                        else
                        {
                            if (before != null && before.IsSpecial)
                            {
                                current.Cost = (before.HiddenCost + current.Price * (decimal)current.Quantity) / (decimal)current.Quantity;
                            }
                            else
                            {
                                current.Cost = current.Price;
                            }
                        }
                    }
                }
            }
            else
            {
                if (before != null)
                {
                    if (before.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn && before.EndingStocks >= 0 && current.DocumentType != (int)BunchOfEnum.DocumentType.StockTake &&
                        (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["IsIgnoreCheckPurchaseReturnDate"]) ||
                         before.TransDate > ConvertHelper.ToDateTime(KvAppConfig.KvTrackingConfigurationBuilder["BeginCheckPurchaseReturnDate"])))
                    {
                        CalculateCostWhenPurchaseReturn(true, current, before);
                    }
                    else
                    {
                        current.Cost = before.Cost;
                        if (before.IsSpecial)
                        {
                            current.IsSpecial = true;
                            current.HiddenCost = before.HiddenCost;
                        }
                        else
                        {
                            current.IsSpecial = false;
                            current.HiddenCost = 0;
                        }
                    }
                    if (before is DummyInventoryTracking && current.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost)
                    {
                        var costAdjustment = await GetCostAdjustmentById(connection, current.DocumentId);
                        if (costAdjustment != null && costAdjustment > 0)
                        {
                            current.Cost = (decimal)costAdjustment;
                        }
                    }
                }
                else
                {
                    if (current.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost)
                    {
                        current.Cost = current.Price;
                    }
                    else
                    {
                        // set currentCost to 0
                        // function try get find available cost will continue process
                        current.Cost = 0;
                    }
                }
            }
        }

        private async Task<decimal> SetNodeValues(InventoryImpact req, ProductFormulaHistoryItem material, CancellationToken cancellationToken = default)
        {
            if (req != null)
            {
                decimal cost;
                var docType = (int)BunchOfEnum.DocumentType.Combo;
                if (req.Track.DocumentType == (int)BunchOfEnum.DocumentType.Refund || req.Track.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo)
                {
                    docType = (int)BunchOfEnum.DocumentType.RefundCombo;
                }
                if (req.Track.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed || req.Track.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo)
                {
                    docType = (int)BunchOfEnum.DocumentType.DeliveryFailedCombo;
                }
                // check reduce return or not
                // remember client pass reversely : true: not reduce, false: reduce
                var isProcess = !req.IsReduceMaterial.HasValue || !req.IsReduceMaterial.Value;
                //If it has no material, it is a leaf. calculate cost it self
                if (material.ListMaterials == null || !material.ListMaterials.Any() || (material.ListMaterials.Any() && material.ProductType != (int)BunchOfEnum.ProductType.Manufactured))
                {
                    using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(req.ConnectionString)))
                    {
                        var itkRepository = new InventoryTrackingRepository(db, _logger);
                        if (docType == (int)BunchOfEnum.DocumentType.Combo ||
                            (req.Track.DocumentType == (int)BunchOfEnum.DocumentType.Refund || req.Track.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo) && !isProcess)
                        {
                            var materilItk = itkRepository.GetTrackingRightBeforeAsync(req.Track.RetailerId, req.Track.BranchId, material.MaterialId, req.Track.TransDate);
                            if (materilItk == null || materilItk.Cost == 0)
                            {
                                var resolveCost = new InventoryTracking
                                {
                                    Cost = 0,
                                    DocumentType = docType,
                                    RetailerId = req.Track.RetailerId,
                                    BranchId = req.Track.BranchId,
                                    ProductId = material.MaterialId,
                                    TransDate = req.Track.TransDate
                                };
                                var proRepository = new ProductRepository(db);
                                var proCache = await proRepository.GetShortProductById(material.MaterialId);
                                await TryResolveCost(db, resolveCost, null, proCache);
                                cost = resolveCost.Cost;
                            }
                            else
                            {
                                cost = materilItk.Cost;
                            }
                        }
                        else
                        {
                            var materilItk = await itkRepository.GetByPrimaryKey(material.MaterialId, req.Track.DocumentId, docType);
                            cost = materilItk?.Cost ?? 0;
                        }
                        cost = cost * (decimal)material.Quantity;
                    }
                    return cost;
                }
                else
                {
                    cost = 0;
                    // recursive to find cost
                    // Make sure all child-nodes have values
                    foreach (var childNode in material.ListMaterials)
                    {
                        cost += await SetNodeValues(req, childNode);
                    }
                    cost = await _multiCurrencyService.Round(req.ConnectionString, req.RetailerId, cost, DefaultRoundFunction, isSpecialCase: true);
                    var costMaster = cost * (decimal)material.Quantity;
                    if (isProcess)
                    {
                        using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(req.ConnectionString)))
                        {
                            var itkRepository = new InventoryTrackingRepository(db, _logger);
                            var materilItk = await itkRepository.GetByPrimaryKey(material.MaterialId, req.Track.DocumentId, docType);
                            if (materilItk == null)
                            {
                                materilItk = new InventoryTracking
                                {
                                    ProductId = material.MaterialId,
                                    DocumentType = docType,
                                    DocumentId = req.Track.DocumentId,
                                    Cost = cost,
                                    RetailerId = req.Track.RetailerId,
                                    Quantity = material.Quantity * req.Track.Quantity,
                                    BranchId = req.Track.BranchId,
                                    DocumentCode = req.Track.DocumentCode,
                                    TransDate = req.Track.TransDate
                                };
                                await itkRepository.Add(materilItk);
                            }
                            else
                            {
                                materilItk.Cost = cost;
                                materilItk.TransDate = req.Track.TransDate;
                                await itkRepository.Update(materilItk);
                            }
                            // reupate product branch if needed
                            var left = itkRepository.GetTrackingLeftAfterAsync(materilItk);
                            if (left != null) return costMaster;
                            var pbRepository = new ProductBranchRepository(db, _logger);
                            var productBranch = await pbRepository.GetProductBranchAsync(material.MaterialId, req.Track.BranchId, req.Track.RetailerId);
                            productBranch.Cost = cost;
                            await pbRepository.Update(productBranch);
                        }
                    }
                    return costMaster;
                }
            }
            return 0;
        }

        #endregion

        #region implement
        public async Task ProcessStockImpact(StockImpact impact, CancellationToken cancellationToken)
        {
            if (impact == null)
            {
                _logger.LogWarning("StockImpact null");
                return;
            }

            await UpdateReservedAsync(impact);
        }
        public async Task ProcessPointImpact(List<PointImpact> impacts, CancellationToken cancellationToken)
        {
            // check if null, return immediately, no-need retry
            if (impacts == null || impacts.Count == 0)
            {
                _logger.LogWarning("List PointImpact null");
                return;
            }
            // process message

            #region group by partition key

            var listPointGroup = (from c in impacts
                                  where c != null
                                  group c by new
                                  {
                                      c.PartitionKey
                                  }
                into gcs
                                  select new
                                  {
                                      gcs.Key.PartitionKey,
                                      Items = gcs.ToList(),
                                  }).ToList();

            #endregion

            // process by each partition key
            foreach (var listPoint in listPointGroup)
            {
                #region init list need rerun
                // init list process
                var listProcess = listPoint.Items;
                // init balanceimpact
                var impact = listProcess.FirstOrDefault();
                if (impact == null) continue;
                // init using when process item
                var listRerun = new List<(PointTracking Tracking, PointTracking RightBeforeTracking, PointTracking TrackingOriginal)>();
                var audit = new PointTrackingObject(listProcess);
                #endregion

                #region calculate it self

                foreach (var item in listProcess.OrderBy(x => x.Track.TransDate))
                {
                    if (item.Advice == BunchOfEnum.ImpactDirection.Add)
                    {
                        var (tracking, rightBeforeTracking, trackingOriginal) = await _pointTrackingHelper.AddOrUpdateTrackingAsync(item);
                        listRerun.Add((tracking, rightBeforeTracking, trackingOriginal));
                    }
                    else
                    {
                        var (tracking, isDeleted) = await _pointTrackingHelper.DeleteTrackingAsync(item);
                        if (!isDeleted) continue;
                        listRerun.Add((tracking, null, null));
                    }
                }

                #endregion

                #region calculate update affected and update last debt

                if (!listRerun.Any()) continue;
                var trackingProcess = listRerun.OrderBy(x => x.Tracking.TransDate).FirstOrDefault();
                if (trackingProcess.Tracking != null)
                {
                    // calculate update affected 
                    var (tracking, isUpdated) = await _pointTrackingHelper.UpdateTrackingSinceAsync(impact,
                        trackingProcess.Tracking, trackingProcess.RightBeforeTracking,
                        trackingProcess.TrackingOriginal);

                    if (!isUpdated)
                    {
                        _logger.LogError($"PointImpact cann't update Affected(s) - PointTracking: {impact.Track.ToJson()}");
                    }

                    if (tracking != null)
                    {
                        // update last debt
                        impact.Track = tracking;
                        await _pointTrackingHelper.UpdateDebtAsync(impact);
                    }
                }

                #endregion
            }
        }

#pragma warning disable S3776
        public async Task ProcessBalanceImpact(List<BalanceImpact> impacts, CancellationToken cancellationToken)
        {
            // check if null, return immediately, no-need retry
            if (impacts == null || impacts.Count == 0)
            {
                _logger.LogWarning("List BalanceImpact null");
                return;
            }

            // process message
            var isRecovery = false;

            #region group by partition key

            var listBalanceGroup = (from c in impacts
                                    where c != null
                                    group c by new
                                    {
                                        c.PartitionKey
                                    }
                into gcs
                                    select new
                                    {
                                        gcs.Key.PartitionKey,
                                        Items = gcs.ToList(),
                                    }).ToList();

            #endregion

            // process by each partition key
            foreach (var listBlance in listBalanceGroup)
            {
                #region init list need rerun
                // init list process
                var listProcess = listBlance.Items;
                // init balanceimpact
                var impact = listProcess.FirstOrDefault();
                if (impact == null) continue;
                // init using when process item
                var listRerun = new List<(BalanceTracking Tracking, BalanceTracking RightBeforeTracking, BalanceTracking TrackingOriginal)>();
                var audit = new BalanceTrackingObject(listProcess);
                #endregion

                #region FNB-6649 [Yêu cầu - Optimize] Thẻ kho: Lock và Log gian hàng nếu số lượng tin đẩy vào Tracking quá nhiều

                if (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["EnableTrackingProcess"]))
                {
                    var countItems = await _balanceTrackingHelper.GetCountAfterByTransDate(impact);
                    if (countItems >= ConvertHelper.ToInt32(KvAppConfig.KvTrackingConfigurationBuilder["TrackingProcessBalance"], 2000))
                    {
                        isRecovery = true;
                        _logger.LogError($"Impact cann't update because the quantity record is too large - BalanceTracking: {impact.Track.ToJson()}");
                        continue;
                    }
                }

                #endregion

                #region calculate it self

                foreach (var item in listProcess.OrderBy(x => x.Track.TransDate))
                {
                    if (item.Advice == BunchOfEnum.ImpactDirection.Add)
                    {
                        var (tracking, rightBeforeTracking, trackingOriginal) = await _balanceTrackingHelper.AddOrUpdateTrackingAsync(item);
                        listRerun.Add((tracking, rightBeforeTracking, trackingOriginal));
                    }
                    else
                    {
                        var (tracking, isDeleted) = await _balanceTrackingHelper.DeleteTrackingAsync(item);
                        if (!isDeleted) continue;
                        listRerun.Add((tracking, null, null));
                    }
                }

                #endregion

                #region calculate update affected and update last debt

                if (!listRerun.Any()) continue;
                var trackingProcess = listRerun.OrderBy(x => x.Tracking.TransDate).FirstOrDefault();
                if (trackingProcess.Tracking != null)
                {
                    // calculate update affected 
                    (_, var isUpdated) = await _balanceTrackingHelper.UpdateTrackingSinceAsync(impact,
                        trackingProcess.Tracking, trackingProcess.RightBeforeTracking,
                        trackingProcess.TrackingOriginal,
                        cancellationToken);

                    if (!isUpdated)
                    {
                        _logger.LogError($"BalanceImpact cann't update Affected(s) - BalanceTracking: {impact.Track.ToJson()}");
                    }
                }

                #endregion
            }

            if (isRecovery)
                throw new KvException(KvStatic.RecoveryMsg);

        }

        public async Task<InventoryTracking> ProcessInventoryImpact(InventoryImpact inventory, int maxProcessItem = 0, CancellationToken cancellationToken = default)
        {
            var logObject = new LogObject(inventory.RetailerId, null);
            // check if null, return immediately, no-need retry
            if (inventory?.Track == null)
            {
                _logger.LogWarning($"InventoryImpact null");
                return null;
            }

            // process message

            #region keep original tracking

            var orginal = new InventoryTracking(inventory.Track);
            var isRecovery = false;

            #endregion


            #region FNB-6649 [Yêu cầu - Optimize] Thẻ kho: Lock và Log gian hàng nếu số lượng tin đẩy vào Tracking quá nhiều
            if (ConvertHelper.ToBoolean(KvAppConfig.KvTrackingConfigurationBuilder["EnableTrackingProcess"]))
            {
                var countItems = await GetCountAfterByTransDate(inventory);
                maxProcessItem = maxProcessItem > 0 ? maxProcessItem : ConvertHelper.ToInt32(KvAppConfig.KvTrackingConfigurationBuilder["TrackingProcessInventory"], 2000);
                if (countItems >= maxProcessItem)
                {
                    var warningMessage = $"Impact cann't update because the quantity record is too large - InventoryTracking: {inventory.Track.ToJson()}";
                    throw new ImpactUpdateWarningException(warningMessage);
                }
            }
            #endregion

            #region check product and retailer

            var prodAndRet = await GetProductAndRetailer(inventory.ConnectionString, inventory.Track.ProductId, inventory.RetailerId, inventory.Track.DocumentType);
            // check if product or retailer null, return immediately, no-need retry
            if (prodAndRet.product == null || prodAndRet.retailer == null)
            {
                _logger.LogError($"InventoryImpact product or retailer null - InventoryTracking: {inventory.Track.ToJson()}");
                return null;
            }
            // check is product master or not, if not, return immediately, no-need retry
            if (prodAndRet.product.MasterUnitId != null)
            {
                _logger.LogError($"InventoryImpact product doesn't master product - InventoryTracking: {inventory.Track.ToJson()}");
                return null;
            }
            inventory.ProductCache = prodAndRet.product;
            inventory.RetailerCache = prodAndRet.retailer;
            #endregion

            #region calculate it self

            (InventoryTracking tracking, InventoryTracking rightBeforeTracking, InventoryTracking trackingOriginal) updateItself = (null, null, null);
            if (inventory.Advice == BunchOfEnum.ImpactDirection.Add)
            {
                updateItself = await AddOrUpdateTrackingAsync(inventory, cancellationToken);
            }
            else
            {
                var deleteItself = await DeleteTrackingAsync(inventory, cancellationToken);
                if (deleteItself.isDeleted)
                {
                    updateItself.tracking = deleteItself.tracking;
                }
                else
                {
                    var exp = new Exception("InventoryImpact cann't delete")
                    {
                        Source = $"PartitionKey: {inventory.PartitionKey} | RetailerId: {inventory.RetailerId}",
                        HelpLink = $"InventoryTracking: {inventory.Track.ToJson()}"
                    };
                        _logger.LogError(exp, "Failed to delete inventory impact");
                    return null; // Stop processing if delete failed unexpectedly
                }
            }

            #endregion

            #region calculate update affected

            // continue to main process
            // for #7056 case 2
            // Ensure tracking is not null before accessing TransDateProcess
            if (updateItself.tracking != null)
            {
                orginal.TransDateProcess = updateItself.tracking.TransDateProcess;
                var updateAffected = await UpdateInventorySinceAsync(inventory, updateItself.tracking, updateItself.rightBeforeTracking, updateItself.trackingOriginal, cancellationToken);
                if (!updateAffected.resultUpdateBatch)
                {
                    _logger.LogError($"InventoryImpact cann't update Affected(s) - InventoryTracking: {inventory.Track.ToJson()}");
                }
                #endregion

                #region reupdate onhand

                await UpdateOnHandAsync(inventory, updateAffected.productId, updateAffected.branchId,
                    updateAffected.retailerId, updateAffected.onHand, updateAffected.cost, updateAffected.lastPrice,
                    updateAffected.lastPriceDocId, updateAffected.listManuExclude, cancellationToken);
                if (inventory.Advice == BunchOfEnum.ImpactDirection.Remove)
                {
                    //ReUpdateLastestPurchasePrice
                    await ReUpdateLastestPurchasePrice(inventory, cancellationToken);
                }
                return updateItself.tracking;
                #endregion
            }
            else
            {
                _logger.LogWarning("updateItself.tracking was null after Add/Delete operation for {ProductId}. Skipping subsequent updates.", inventory.Track.ProductId);
            }

            if (isRecovery)
                throw new KvException(KvStatic.RecoveryMsg);
            return updateItself.tracking;
        }

#pragma warning restore S3776        


        public async Task<(Product product, Retailer retailer)> GetProductAndRetailer(string connStr, long productId, int retailerId, int docType, CancellationToken cancellationToken = default)
        {
            if (productId == 0 || retailerId == 0) return (null, null);
            var product = await _productService.GetProductById(connStr, productId);
            
            var retailer = await _productService.GetRetailerPosParam(connStr, retailerId);

            return (product, retailer);
        }

        public async Task<(InventoryTracking tracking, InventoryTracking rightBeforeTracking, InventoryTracking trackingOriginal)>
            AddOrUpdateTrackingAsync(InventoryImpact tracking, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(tracking.ConnectionString)))
            {
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                var before = itkRepository.GetTrackingRightBeforeAsync(tracking.Track);
                //update ending stock 
                UpdateEndingStock(tracking, before);
                // update Cost
                await UpdateCost(db, tracking.ConnectionString, tracking.RetailerCache.UseAvgCost, tracking.Track, before, tracking.ProductCache, null, null);
                var existing = await itkRepository.GetByPrimaryKey(tracking.Track.ProductId, tracking.Track.DocumentId, (BunchOfEnum.DocumentType)tracking.Track.DocumentType);
                var cost = await _multiCurrencyService.Round(tracking.ConnectionString, tracking.RetailerId, tracking.Track.Cost, DefaultRoundFunction, isSpecialCase: true);
                if (existing != null)
                {
                    var oldCopy = new InventoryTracking();
                    oldCopy.CopyFrom(existing);
                    oldCopy.TransDate = tracking.Track.OriginalTransDate ?? existing.TransDate;
                    existing.TransDate = tracking.Track.TransDate;
                    existing.Quantity = tracking.Track.Quantity;
                    existing.Price = tracking.Track.Price;
                    existing.EndingStocks = tracking.Track.EndingStocks;
                    existing.Cost = cost;
                    existing.HiddenCost = tracking.Track.HiddenCost;
                    existing.IsSpecial = tracking.Track.IsSpecial;


                    await itkRepository.Update(existing);
                    if (oldCopy.TransDate < tracking.Track.TransDate)
                    {
                        var rightBeforeTracking = itkRepository.GetTrackingRightBeforeAsync(oldCopy);
                        if (rightBeforeTracking != null)
                        {
                            tracking.Track.TransDateProcess = rightBeforeTracking.TransDate;
                            return (tracking: rightBeforeTracking, rightBeforeTracking: default(InventoryTracking), trackingOriginal: existing);
                        }
                        else
                        {
                            tracking.Track.TransDateProcess = new DateTime(1900, 1, 1);
                            return (tracking: GetDummyTracking(tracking.Track.ProductId, tracking.Track.RetailerId, tracking.Track.BranchId)
                                , rightBeforeTracking: default(InventoryTracking), trackingOriginal: existing);
                        }
                    }
                    else
                    {
                        var rightBeforeTracking = before ?? GetDummyTracking(tracking.Track.ProductId, tracking.Track.RetailerId, tracking.Track.BranchId);
                        tracking.Track.TransDateProcess = tracking.Track.TransDate;
                        return (tracking: existing, rightBeforeTracking: rightBeforeTracking, trackingOriginal: default(InventoryTracking));
                    }
                }
                else
                {
                    tracking.Track.Cost = cost;

                    await itkRepository.Add(tracking.Track);
                    var rightBeforeTracking = before ?? GetDummyTracking(tracking.Track.ProductId, tracking.Track.RetailerId, tracking.Track.BranchId);
                    tracking.Track.TransDateProcess = tracking.Track.TransDate;
                    return (tracking: tracking.Track, rightBeforeTracking: rightBeforeTracking, trackingOriginal: default(InventoryTracking));
                }
            }
        }

        public async
            Task<(long productId, int branchId, int retailerId, double onHand, decimal cost, decimal? lastPrice, long? lastPriceDocId, bool
                resultUpdateBatch, List<long> listManuExclude)> UpdateInventorySinceAsync(InventoryImpact request,
                InventoryTracking tracking,
                InventoryTracking rightBeforeTracking = null,
                InventoryTracking trackingOriginal = null,
                CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(request.ConnectionString)))
            {
                decimal? lastPrice = null;
                long? lastPriceDocId = null;
                IList<InventoryTracking> listComboAffected = null;
                IList<InventoryTracking> listManuAffected = null;
                IList<InventoryTracking> listCustomizableComboParentIventory = new List<InventoryTracking>();
                var listManuExclude = new List<long>();
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                var invoiceDetailRepository = new InvoiceDetailRepository(db);
                tracking = await itkRepository.GetByPrimaryKey(tracking.ProductId, tracking.DocumentId, (BunchOfEnum.DocumentType)tracking.DocumentType) ?? tracking;
                //get affected trackings since the tracking of concern onward 
                //NOTE: the cutoff range MUST be t.TransDate >= tracking.TransDate 
                //to update other transaction that is on the same date (will happens when user change date using date picker)
                var items = await itkRepository.GetAfterByTransDate(tracking);
                //a little trick to make certain of time order 
                //whenever there is document of the same date ( to degree of second ) 
                //it won't matter which one ACTUALLY comes first 
                //we can enforce an order 
                //NOTE ** for SQL DateTime : Accuracy – Rounded to increments of .000, .003, or .007 seconds
                //Remember to use Datetime2 instead - with at least precision of 3
                var firstTrackingMoved = false;
                var cursor = tracking;
                foreach (var item in items)
                {
                    var isChange = false;
                    var listCheck = items.Where(x => x.TransDate == cursor.TransDate && x != cursor).ToList();
                    if (!listCheck.Any())
                        isChange = true;
                    if (listCheck.Any())
                    {
                        foreach (var other in listCheck)
                        {
                            if (!firstTrackingMoved) firstTrackingMoved = true;
                            if (cursor.DocumentType.Equals(other.DocumentType))
                            {
                                var cursorCode = "1";
                                var otherCode = "2";
                                if (cursor.DocumentCode.Length > 8 && other.DocumentCode.Length > 8)
                                {
                                    cursorCode = cursor.DocumentCode.Substring(cursor.DocumentCode.Length - 8);
                                    otherCode = other.DocumentCode.Substring(other.DocumentCode.Length - 8);
                                }
                                if (cursorCode.Equals(otherCode))
                                {
                                    if (cursor.DocumentId > other.DocumentId)
                                    {
                                        cursor.TransDate = cursor.TransDate.AddMilliseconds(KvStatic.ForcePaymentLag);
                                    }
                                    else
                                    {
                                        other.TransDate = other.TransDate.AddMilliseconds(KvStatic.ForcePaymentLag);
                                        cursor = other;
                                    }
                                }
                                else
                                {
                                    var maxItem = items.Where(x => x.TransDate.Year == other.TransDate.Year
                                                                   && x.TransDate.Month == other.TransDate.Month
                                                                   && x.TransDate.Day == other.TransDate.Day
                                                                   && x.TransDate.Hour == other.TransDate.Hour
                                                                   && x.TransDate.Minute == other.TransDate.Minute).Max(x => x.TransDate);
                                    if (trackingOriginal != null &&
                                        trackingOriginal.DocumentId == other.DocumentId &&
                                        trackingOriginal.DocumentCode == other.DocumentCode &&
                                        trackingOriginal.DocumentType == other.DocumentType)
                                    {
                                        other.TransDate = other.TransDate.AddMilliseconds(KvStatic.ForcePaymentLag);
                                        // fix #11473 in customer support, when trackingOriginal is the last, and transdate have duplicate
                                        // add re-check maxDate again                                        
                                        if (other.TransDate == maxItem)
                                        {
                                            other.TransDate = maxItem.AddMilliseconds(KvStatic.ForcePaymentLag);
                                        }
                                        cursor = other;
                                    }
                                    else
                                    {
                                        cursor.TransDate = maxItem.AddMilliseconds(KvStatic.ForcePaymentLag);
                                    }
                                }
                            }
                            else
                            {
                                cursor.TransDate = cursor.TransDate.AddMilliseconds(KvStatic.ForcePaymentLag);
                            }
                        }
                    }
                    if (isChange)
                        cursor = item;
                }
                var previous = tracking;
                var previousFixedCost = tracking.Cost;
                if (firstTrackingMoved)
                {
                    //insert tracking into items so that it get updated as well 
                    var temp = items.ToList();
                    var i = 0;
                    for (; i < temp.Count; i++)
                    {
                        var item = temp[i];
                        if (item.TransDate <= tracking.TransDate) continue;
                        temp.Insert(i, tracking);
                        break;
                    }
                    if (i == temp.Count)
                    {
                        temp.Add(tracking);
                    }
                    items = temp;
                    //most likely, it won't happen that rightBeforeTracking is null
                    //_getDummyTracking is just a precaution 
                    if (rightBeforeTracking == null)
                    {
                        previous = itkRepository.GetTrackingRightBeforeAsync(items.First());
                    }
                    else
                    {
                        if (rightBeforeTracking.TransDate.Date == new DateTime(1900, 1, 1).Date)
                        {
                            previous = rightBeforeTracking;
                            // keep cost for the first-time
                            previous.Cost = items.First().Cost;
                        }
                        else
                        {
                            previous = rightBeforeTracking;
                        }
                    }

                    // Resolve issue 0 cost when apply TrackingV4 without Using AvgCost in #FNB-58484
                    previousFixedCost = previous.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost || previous.Cost > 0 ? previous.Cost : previousFixedCost;
                }
                items = items.OrderBy(x => x.TransDate).ThenBy(x => x.DocumentId).ToList();
                foreach (var item in items)
                {
                    UpdateEndingStock(item, previous);
                    var oldCost = item.Cost;
                    if (request.RetailerCache.UseAvgCost && request.ProductCache.ProductType != (int)BunchOfEnum.ProductType.Manufactured &&
                        request.ProductCache.ProductType != (int)BunchOfEnum.ProductType.Service)
                    {
                        Return returnRetrieve = null;
                        // fix 7201, detect refund of invoice re-calculate cost in one time
                        if (item.DocumentType == (int)BunchOfEnum.DocumentType.Refund || item.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo)
                        {
                            var returnRepository = new ReturnRepository(db);
                            returnRetrieve = await returnRepository.GetByIdAsync(item.DocumentId);
                            if (returnRetrieve != null)
                            {
                                returnRetrieve.IsFound = true;
                            }
                            if (returnRetrieve?.InvoiceId != null)
                            {
                                var inventoryTracking = items.FirstOrDefault(x =>
                                    x.ProductId == item.ProductId && x.DocumentId == returnRetrieve.InvoiceId &&
                                    x.DocumentType == (item.DocumentType == (int)BunchOfEnum.DocumentType.Refund
                                        ? (int)BunchOfEnum.DocumentType.Invoice
                                        : (int)BunchOfEnum.DocumentType.Combo));
                                if (inventoryTracking != null)
                                {
                                    returnRetrieve.IsHaveTracking = true;
                                    returnRetrieve.InvoiceCost = inventoryTracking.Cost;
                                }
                            }
                        }
                        // fix 7201, detect invoice delivery failed of invoice re-calculate cost in one time
                        InventoryTracking invoiceRetrieve = null;
                        if (item.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed || item.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo)
                        {
                            var inventoryTracking = items.FirstOrDefault(x =>
                                x.ProductId == item.ProductId && x.DocumentId == item.DocumentId &&
                                x.DocumentType == (item.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailed
                                    ? (int)BunchOfEnum.DocumentType.Invoice
                                    : (int)BunchOfEnum.DocumentType.Combo));
                            if (inventoryTracking != null)
                            {
                                invoiceRetrieve = new InventoryTracking(inventoryTracking);
                            }
                        }
                        await UpdateCost(db, request.ConnectionString, request.RetailerCache.UseAvgCost, item, previous, request.ProductCache, returnRetrieve, invoiceRetrieve);
                    }
                    if (item.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseInvoice)
                    {
                        var pudRepository = new PurchaseOrderDetailRepository(db);
                        var latest = await pudRepository.GetByPurchaseProduct(item.DocumentId, item.ProductId);
                        // #5072 remove discount (latest?.Price- latest?.Discount)
                        lastPrice = latest?.Price ?? item.Price;
                        lastPriceDocId = item.DocumentId;
                    }
                    previous = item;

                    // Resolve issue 0 cost when apply TrackingV4 without Using AvgCost in #FNB-58484
                    previousFixedCost = item.DocumentType == (int)BunchOfEnum.DocumentType.ChangeCost || item.Cost > 0 ? item.Cost : previousFixedCost;

                    var newCost = item.Cost;
                    var isBiggerTolerance = Math.Abs(Math.Round(oldCost, 4) - Math.Round(newCost, 4)) > (decimal)KvStatic.ToleranceChangeCost;
                    if (isBiggerTolerance &&
                        (item.DocumentType == (int)BunchOfEnum.DocumentType.Combo ||
                         item.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo ||
                         item.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo) &&
                        item.DocumentId != request.Track.DocumentId)
                    {
                        if (listComboAffected == null) listComboAffected = new List<InventoryTracking>();
                        listComboAffected.Add(new InventoryTracking(item));
                    }

                    if (isBiggerTolerance &&
                        item.DocumentType == (int)BunchOfEnum.DocumentType.ManufacturingMaterial &&
                        item.DocumentId != request.Track.DocumentId)
                    {
                        // check inNeed re-run
                        // optimize check only one manu need run if manu as the same
                        var isExistInManu = await itkRepository.IsHaveManufacturingByManuIdMaterialId(item.DocumentId, item.ProductId);
                        if (isExistInManu == null || !(isExistInManu > 0)) continue;
                        if (listManuAffected == null) listManuAffected = new List<InventoryTracking>();
                        var tempTracking = new InventoryTracking(item) { ProductId = (long)isExistInManu };
                        if (listManuAffected.All(x => x.ProductId != tempTracking.ProductId))
                        {
                            listManuAffected.Add(tempTracking);
                        }
                    }
                
                    if (item.DocumentType == (int)DocumentType.CustomizebleCombo && _unleashService.IsEnable(FeatureToggles.UpdateCustomizableComboParentCost))
                    {
                        var parentProductId = await invoiceDetailRepository.GetParentCustomizableComboProductId(item.DocumentId, item.ProductId);
                        if (parentProductId != null)
                        {
                            var parentIvt = await itkRepository.GetByPrimaryKey(parentProductId.Value, item.DocumentId, DocumentType.Invoice);
                            if (parentIvt != null)
                            {
                                parentIvt.Cost += (newCost - oldCost) * (decimal)(item.Quantity / parentIvt.Quantity);
                                listCustomizableComboParentIventory.Add(parentIvt);
                            }
                        }
                    }
                }
                if (lastPrice == null && tracking.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseInvoice)
                {
                    var pudRepository = new PurchaseOrderDetailRepository(db);
                    var latest = await pudRepository.GetByPurchaseProduct(tracking.DocumentId, tracking.ProductId);
                    // #5072 remove discount (latest?.Price- latest?.Discount)
                    lastPrice = latest?.Price ?? tracking.Price;
                    lastPriceDocId = tracking.DocumentId;
                }

                foreach (var item in listCustomizableComboParentIventory)
                {
                    items.Add(item);
                }

                items = await RoundCostAsync(request.ConnectionString, request.RetailerId, items);
                var resultUpdateBatch = await itkRepository.UpdateBatch(items);
                
                // process another combo affected by each document
                if (listComboAffected != null && listComboAffected.Any())
                {
                    foreach (var item in listComboAffected)
                    {
                        var list = new List<TransDetailShort>();
                        list.AddRange(await _productService.GetComboIdInTransaction(request.ConnectionString, item));
                        if (!list.Any()) continue;
                        var docType = (int)BunchOfEnum.DocumentType.Invoice;
                        if (item.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo)
                        {
                            docType = (int)BunchOfEnum.DocumentType.Refund;
                        }
                        if (item.DocumentType == (int)BunchOfEnum.DocumentType.DeliveryFailedCombo)
                        {
                            docType = (int)BunchOfEnum.DocumentType.DeliveryFailed;
                        }
                        var dictSend = new Dictionary<long, InventoryTracking>();
                        // convert list to IventoryTracking list and send to kafka
                        foreach (var combo in list)
                        {
                            if (docType == (int)BunchOfEnum.DocumentType.Invoice)
                            {
                                combo.Quantity *= -1;
                            }
                            if (dictSend.ContainsKey(combo.ProductId))
                            {
                                dictSend[combo.ProductId].Quantity += combo.Quantity;
                            }
                            else
                            {
                                dictSend[combo.ProductId] = new InventoryTracking
                                {
                                    DocumentType = docType,
                                    ProductId = combo.ProductId,
                                    BranchId = item.BranchId,
                                    DocumentCode = item.DocumentCode,
                                    DocumentId = item.DocumentId,
                                    ProductType = (byte)BunchOfEnum.ProductType.Manufactured,
                                    Quantity = combo.Quantity,
                                    RetailerId = item.RetailerId,
                                    TransDate = item.TransDate
                                };
                            }
                        }
                        var trans = new TrackingTransaction(dictSend.Values, BunchOfEnum.ImpactDirection.Update, request.ConnectionString);
                        var message = new Message<string, string>
                        {
                            Key = trans.PartitionKey,
                            Value = trans.ToJson()
                        };
                        await _trackingKafkaProducer.ProduceAsync(trans.Topic, message);
                    }
                }

                // process manufacturing affected by each document
                if (listManuAffected != null && listManuAffected.Any())
                {
                    foreach (var manu in listManuAffected)
                    {
                        try
                        {
                            var trans = new TrackingTransaction(manu, BunchOfEnum.ImpactDirection.Add, request.ConnectionString);
                            var message = new Message<string, string>
                            {
                                Key = trans.PartitionKey,
                                Value = trans.ToJson()
                            };
                            await _trackingKafkaProducer.ProduceAsync(trans.Topic, message);
                            listManuExclude.Add(manu.DocumentId);
                        }
                        catch (Exception ex)
                        {
                            var item = items.FirstOrDefault();
                            _logger.LogError(ex, $"{ex.Message} : {item.DocumentCode}");
                        }
                    }
                }

                //if previous is advanced to very last record, update OnHand 
                if (previous != null && previous.ProductId != 0)
                {
                    previousFixedCost = request.RetailerCache.UseAvgCost ? previous.Cost : previousFixedCost;

                    return (productId: previous.ProductId, branchId: previous.BranchId, retailerId: previous.RetailerId,
                        onHand: previous.EndingStocks, cost: previousFixedCost, lastPrice: lastPrice, lastPriceDocId: lastPriceDocId, resultUpdateBatch:
                        resultUpdateBatch, listManuExclude: listManuExclude);
                }
                return (productId: tracking.ProductId, branchId: tracking.BranchId, retailerId: tracking.RetailerId,
                    onHand: tracking.EndingStocks, cost: tracking.Cost, lastPrice: lastPrice, lastPriceDocId: lastPriceDocId, resultUpdateBatch:
                    resultUpdateBatch, listManuExclude: listManuExclude);
            }
        }

        public async Task UpdateOnHandAsync(InventoryImpact request, long productId, int branchId, int retailerId,
            double onHand,
            decimal cost, decimal? lastPrice, long? lastPriceDocId, List<long> listManuExclude, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(request.ConnectionString)))
            {
                var pbRepository = new ProductBranchRepository(db, _logger);
                var productBranch = await pbRepository.GetProductBranchAsync(productId, branchId, retailerId);
                var oldVal = productBranch.Cost;
                var newOnHand = request.ProductCache.ProductType == (int)BunchOfEnum.ProductType.Purchased ? onHand : 0;
                if (!_unleashService.IsEnable(UsingInventoryTrackingV4))
                {
                    productBranch.OnHand = newOnHand;
                }
                
                if (lastPrice != null)
                {
                    productBranch.LatestPurchasePrice = lastPrice.GetValueOrDefault();
                }
                productBranch.ModifiedDate = DateTime.Now;
                if (request.ProductCache.ProductType == (int)BunchOfEnum.ProductType.Purchased)
                {
                    if (cost.Equals(0) &&
                        request.ProductCache.Formula != null && request.ProductCache.Formula.Count > 0)
                    {
                        var itkRepository = new InventoryTrackingRepository(db, _logger);
                        var count = await itkRepository.GetCountInventoryTrackingByProduct(productId, retailerId, branchId);
                        if (count == null || count.Equals(0))
                        {
                            var materials = await pbRepository.GetLastestMaterialsByProductId(retailerId, productId);
                            if (materials != null && materials.Count > 0)
                            {
                                decimal costMaterials = 0;
                                foreach (var material in materials)
                                {
                                    costMaterials += await SetNodeValues(request.ConnectionString, retailerId, branchId, material);
                                }
                                productBranch.Cost = costMaterials;
                            }
                        }
                        else
                        {
                            productBranch.Cost = cost;
                        }
                    }
                    else
                    {
                        productBranch.Cost = cost;
                    }
                }

                await pbRepository.Update(productBranch);
                var listChildUnit = request.ProductCache.ProductChildUnit;
                if (listChildUnit != null && request.ProductCache.ProductType == (int)BunchOfEnum.ProductType.Purchased)
                {
                    foreach (var item in listChildUnit.Where(item => item.ProductType == (int)BunchOfEnum.ProductType.Purchased))
                    {
                        var productunitBranch = await pbRepository.GetProductBranchAsync(item.Id, branchId, retailerId);
                        if (productunitBranch != null)
                        {
                            if (!_unleashService.IsEnable(UsingInventoryTrackingV4))
                                productunitBranch.OnHand = item.ConversionValue > 0
                                    ? Math.Round((onHand / item.ConversionValue), 3)
                                    : 0;
                            productunitBranch.Cost = item.ConversionValue > 0
                                ? ConvertHelper.RoundDecimal(cost * (decimal)item.ConversionValue)
                                : 0;
                            productunitBranch.ModifiedDate = DateTime.Now;
                            // Update Last Price for Product Unit
                            if (lastPrice != null)
                            {
                                // update #13870, check if converse product exists in transaction
                                PurchaseOrderDetail latest = null;
                                if (lastPriceDocId != null)
                                {
                                    var pudRepository = new PurchaseOrderDetailRepository(db);
                                    latest = await pudRepository.GetByPurchaseProduct((long)lastPriceDocId, item.Id);
                                }
                                if (latest != null)
                                {
                                    productunitBranch.LatestPurchasePrice = latest.Price;
                                }
                                else
                                {
                                    productunitBranch.LatestPurchasePrice = lastPrice.GetValueOrDefault() * (decimal)item.ConversionValue;
                                }
                            }
                            await pbRepository.Update(productunitBranch);
                        }
                    }
                }
                // detect master of product
                // detect manufacturing of master and re-run
                if (request.RetailerCache.UseAvgCost && request.ProductCache.ProductType.Equals((int)BunchOfEnum.ProductType.Purchased)
                    && request.ProductCache.MasterFormula != null && request.ProductCache.MasterFormula.Count > 0)
                {
                    // optimize #7208
                    // check only cost change, we must re-run manu affected
                    if (Math.Abs(Math.Round(oldVal, 4) - Math.Round(productBranch.Cost, 4)) > (decimal)KvStatic.ToleranceChangeCost)
                    {
                        var itkRepository = new InventoryTrackingRepository(db, _logger);
                        // apply for manufacturing product only (not include combo)
                        var listProducts = request.ProductCache.MasterFormula.Where(x => x.ProductType == (int)BunchOfEnum.ProductType.Purchased).Select(x => x.Id).ToList();
                        if (listProducts.Any())
                        {
                            var dateProcess = request.Track?.TransDateProcess ?? new DateTime(1900, 1, 1);
                            var dateDiff = dateProcess - new DateTime(1900, 1, 1);
                            if (dateDiff.TotalDays < 0)
                            {
                                dateProcess = new DateTime(1900, 1, 1);
                            }

                            var excludeId = new List<long>();
                            if (request.Track != null && request.Track.DocumentType == (int)BunchOfEnum.DocumentType.ManufacturingMaterial)
                            {
                                excludeId.Add(request.Track.DocumentId);
                            }
                            if (listManuExclude.Any())
                            {
                                excludeId.AddRange(listManuExclude);
                            }
                            var listManu = await itkRepository.GetManufacturingByProductIdAfterTransDate(listProducts, dateProcess, excludeId, branchId, retailerId);
                            if (listManu.Any())
                            {
                                // optimize check only one manu need run if manu as the same
                                var listManuTemp = listManu
                                    .GroupBy(item => new { item.ProductId, item.RetailerId, item.BranchId })
                                    .Select(group => new Manufacturing
                                    {
                                        Id = group.First().Id,
                                        Code = group.First().Code,
                                        ProductId = group.Key.ProductId,
                                        Quantity = group.First().Quantity,
                                        RetailerId = group.Key.RetailerId,
                                        BranchId = group.Key.BranchId,
                                        ManufacturingDate = group.First().ManufacturingDate
                                    })
                                    .OrderBy(item => item.ManufacturingDate);
                                foreach (var manu in listManuTemp)
                                {
                                    try
                                    {
                                        // assign = 0 cause need re-get correct productId
                                        var trk = new InventoryTracking
                                        {
                                            ProductId = 0,
                                            DocumentType = (int)BunchOfEnum.DocumentType.Manufacturing,
                                            BranchId = manu.BranchId,
                                            DocumentCode = manu.Code,
                                            DocumentId = manu.Id,
                                            Quantity = manu.Quantity,
                                            RetailerId = manu.RetailerId,
                                            TransDate = manu.ManufacturingDate
                                        };
                                        var trans = new TrackingTransaction(trk, BunchOfEnum.ImpactDirection.Add, request.ConnectionString);
                                        var topic = "ManufacturingTrackingService" + KvAppConfig.KvTrackingConfigurationBuilder["Kafka:KafkaPrefix"];
                                        var message = new Message<string, string>
                                        {
                                            Key = trans.PartitionKey,
                                            Value = trans.ToJson()
                                        };
                                        await _trackingKafkaProducer.ProduceAsync(topic, message);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogError(ex, $"{ex.Message}");
                                    }
                                }
                            }
                        }
                    }
                }

                // update https://citigo.assembla.com/spaces/kiotviet-api/tickets/1289--sprint21--fnb--h%E1%BB%97-tr%E1%BB%A3-t%C3%ADnh-n%C4%83ng-c%E1%BA%A3nh-b%C3%A1o-t/details?comment=1655040981
                if ((request.Track.DocumentType == (int)BunchOfEnum.DocumentType.StockTake ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.Invoice ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.Tranfer ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.Receive ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.DamageItem ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.Combo ||
                    request.Track.DocumentType == (int)BunchOfEnum.DocumentType.ManufacturingMaterial)
                    && newOnHand < productBranch.MinQuantity && request.ProductCache.InventoryTrackingIgnore != true
                    )
                {
                    var branchRepo = new ProductBranchRepository(db, _logger);

                    var bName = await GetBranchNameFromCache(_cacheClient, productBranch.RetailerId, branchId, branchRepo);
                    var pName = await GetProductNameFromCache(_cacheClient, productBranch.RetailerId, productBranch.ProductId, branchRepo);
                    var msg = new Msg
                    {
                        Data = new MainMsgNoti
                        {
                            MessageId = Guid.NewGuid(),
                            BranchId = productBranch.BranchId,
                            BranchName = bName.Name,
                            RetailerId = productBranch.RetailerId,
                            NotifyMessage = $"{pName} dưới định mức tồn tại {bName.Name}.",
                            EventType = "Product_OnHand",
                            CreatedDate = DateTime.Now,
                            Shard = request.RetailerCache.GroupId,
                            DocumentId = request.Track.ProductId,
                            Extra = $"{pName}"
                        }
                    };

                    await _trackingNotiRabbitProducer.ProduceAsync(msg.ToJson(), _trackingNotiRabbitConfiguration.RabbitMq.QueueName, cancellationToken);
                }
            }
        }

        private static async Task<string> GetProductNameFromCache(ICacheClient _cacheClient, int retailerId, long productId, ProductBranchRepository branchRepo)
        {
            return await _cacheClient.GetAsync(string.Format(TrackingProductCacheKey, retailerId, productId), async (key) =>
            {
                return await branchRepo.GetProductFullNameNoLock(productId);
            }, TimeSpan.FromDays(1));
        }

        private static async Task<Branch> GetBranchNameFromCache(ICacheClient _cacheClient, int retailerId, int branchId, ProductBranchRepository branchRepo)
        {
            return await _cacheClient.GetAsync(string.Format(TrackingBranchCacheKey, retailerId, branchId), async (key) =>
            {
                return await branchRepo.GetBranchNoLock(branchId);
            }, TimeSpan.FromDays(7));
        }

        public async Task<(InventoryTracking tracking, bool isDeleted)> DeleteTrackingAsync(InventoryImpact tracking, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(tracking.ConnectionString)))
            {
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                var existing = await itkRepository.GetByPrimaryKey(tracking.Track.ProductId, tracking.Track.DocumentId, tracking.Track.DocumentType);
                if (existing != null)
                {
                    await itkRepository.DeleteByKey(existing.ProductId, existing.DocumentId, existing.DocumentType);
                    var rightBefore = itkRepository.GetTrackingRightBeforeAsync(existing);
                    tracking.Track.TransDateProcess = rightBefore?.TransDate ?? new DateTime(1900, 1, 1);
                    return (tracking: rightBefore ?? GetDummyTracking(existing.ProductId, existing.RetailerId, existing.BranchId), isDeleted: true);
                }
                else
                {
                    tracking.Track.TransDateProcess = new DateTime(1900, 1, 1);
                    var rightBefore = itkRepository.GetTrackingRightBeforeAsync(tracking.Track);
                    return (tracking: rightBefore ?? GetDummyTracking(tracking.Track.ProductId, tracking.Track.RetailerId, tracking.Track.BranchId), isDeleted: true);
                }
            }
        }

        public async Task ReUpdateLastestPurchasePrice(InventoryImpact request, CancellationToken cancellationToken = default)
        {
            if (request.Track.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseInvoice ||
                request.Track.DocumentType == (int)BunchOfEnum.DocumentType.PurchaseReturn)
            {
                using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(request.ConnectionString)))
                {
                    var itkRepository = new InventoryTrackingRepository(db, _logger);
                    var purchaseLast = await itkRepository.GetPurchaseAfter(request.Track);
                    if (purchaseLast != null) return;
                    var purchase = await itkRepository.GetPurchaseBefore(request.Track);
                    var pudRepository = new PurchaseOrderDetailRepository(db);
                    var latest = await pudRepository.GetByPurchaseProduct(purchase?.DocumentId ?? -1, purchase?.ProductId ?? -1);
                    var purchasePrice = purchase?.Price ?? 0;
                    var lastPrice = latest?.Price ?? purchasePrice;
                    var pbRepository = new ProductBranchRepository(db, _logger);
                    var productBranch = await pbRepository.GetProductBranchByPrimaryKeyAsync(request.Track.ProductId, request.Track.BranchId, request.RetailerId);
                    if (productBranch == null) return;
                    productBranch.ModifiedDate = DateTime.Now;
                    productBranch.LatestPurchasePrice = lastPrice;
                    await pbRepository.Update(productBranch);
                    if (request.ProductCache.ProductChildUnit != null &&
                        request.ProductCache.ProductChildUnit.Count > 0 && request.ProductCache.ProductType == (int)BunchOfEnum.ProductType.Purchased)
                    {
                        foreach (var item in request.ProductCache.ProductChildUnit.Where(x => x.ProductType == (int)BunchOfEnum.ProductType.Purchased))
                        {
                            var productunitBranch = await pbRepository.GetProductBranchAsync(item.Id, request.Track.BranchId, request.RetailerId);
                            productunitBranch.ModifiedDate = DateTime.Now;
                            // update #13870, check if converse product exists in transaction
                            var latestConverse = await pudRepository.GetByPurchaseProduct(purchase?.DocumentId ?? -1, item.Id);
                            if (latestConverse != null)
                            {
                                productunitBranch.LatestPurchasePrice = latestConverse.Price;
                            }
                            else
                            {
                                productunitBranch.LatestPurchasePrice = (purchase?.Price ?? 0) * (decimal)item.ConversionValue;
                            }
                            await pbRepository.Update(productunitBranch);
                        }
                    }
                }
            }
        }

        public async Task UpdateReservedAsync(StockImpact impact, CancellationToken cancellationToken = default)
        {
            await _productService.UpdateReservedAsync(impact.ConnectionString, impact.ProductId,
                impact.BranchId, impact.RetailerId, impact.ReservedChange, impact.OnOrderChange,
                impact.TransferToChange, cancellationToken);
        }

        public async Task AddOrUpdateComboTrackingAsync(InventoryImpact req, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(req.ConnectionString)))
            {
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                var pbRepository = new ProductBranchRepository(db, _logger);
                if (req.Advice == BunchOfEnum.ImpactDirection.Add || req.Advice == BunchOfEnum.ImpactDirection.Update)
                {
                    var canProcess = true;
                    if (req.Advice == BunchOfEnum.ImpactDirection.Update)
                    {
                        var existing = await itkRepository.GetByPrimaryKey(req.Track.ProductId, req.Track.DocumentId, req.Track.DocumentType);
                        if (existing == null)
                        {
                            canProcess = false;
                        }
                    }
                    if (canProcess)
                    {
                        var materials = new List<ProductFormulaHistoryItem>();
                        if (req.TreeLevel > 1)
                        {
                            // approve for combo have level > 1
                            // find docType
                            var docType = BunchOfEnum.DocumentType.Invoice;
                            if (req.Track.DocumentType == (int)BunchOfEnum.DocumentType.RefundCombo)
                            {
                                docType = BunchOfEnum.DocumentType.Refund;
                            }
                            // find productId 
                            var proId = req.Track.ProductId;
                            if (req.Track.MainComboTracking.Any())
                            {
                                var firstMain = req.Track.MainComboTracking.FirstOrDefault();
                                if (firstMain != null)
                                {
                                    proId = firstMain.ProductId;
                                }
                            }
                            var tempMaterials = await pbRepository.GetAllMaterialsByFormulaHistory(req.Track.RetailerId, req.Track.DocumentId, proId, docType);
                            if (tempMaterials != null && tempMaterials.Any())
                            {
                                foreach (var root in tempMaterials)
                                {
                                    var node = root?.GetNodeAndDescendants().Where(x => x.ProductId == req.Track.ProductId).ToList();
                                    if (node == null || !node.Any()) continue;
                                    materials.AddRange(node);
                                    break;
                                }
                            }
                        }
                        else
                        {
                            // approve for combo have level = 1 (root)
                            // find tree material
                            materials = await pbRepository.GetAllMaterialsByFormulaHistory(req.Track.RetailerId,
                                req.Track.DocumentId, req.Track.ProductId,
                                (BunchOfEnum.DocumentType)req.Track.DocumentType);
                        }

                        // calculate cost
                        decimal cost = 0;
                        if (materials != null && materials.Count > 0)
                        {
                            foreach (var material in materials)
                            {
                                cost += await SetNodeValues(req, material);
                            }
                        }
                        // insert or update
                        var mainCombo = await itkRepository.GetByPrimaryKey(req.Track.ProductId, req.Track.DocumentId, req.Track.DocumentType);
                        cost = await _multiCurrencyService.Round(req.ConnectionString, req.RetailerId, cost, DefaultRoundFunction, isSpecialCase: true);
                        if (mainCombo == null)
                        {
                            mainCombo = req.Track;
                            mainCombo.Cost = cost;
                            await itkRepository.Add(mainCombo);
                        }
                        else
                        {
                            mainCombo.TransDate = req.Track.TransDate;
                            mainCombo.Cost = cost != 0 ? cost : mainCombo.Cost;
                            mainCombo.Quantity = req.Track.Quantity;
                            await itkRepository.Update(mainCombo);
                        }
                        // reupate product branch if needed
                        var left = itkRepository.GetTrackingLeftAfterAsync(mainCombo);
                        if (left == null)
                        {
                            // check if no left then update lastest cost of current formula
                            await UpdateLatestCostCombo(db, req.ConnectionString, req.Track.RetailerId, req.Track.BranchId, req.Track.ProductId);
                        }
                    }
                }
                else
                {
                    // remove combo
                    var existing = await itkRepository.GetByPrimaryKey(req.Track.ProductId, req.Track.DocumentId, req.Track.DocumentType);
                    if (existing != null)
                    {
                        await itkRepository.DeleteByKey(existing.ProductId, existing.DocumentId, existing.DocumentType);
                        // reupate product branch if needed
                        var left = itkRepository.GetTrackingLeftAfterAsync(existing);
                        if (left == null)
                        {
                            // check if no left then update lastest cost of current formula
                            await UpdateLatestCostCombo(db, req.ConnectionString, req.Track.RetailerId, req.Track.BranchId, req.Track.ProductId);
                        }
                    }
                }
            }
        }

        public async Task<Manufacturing> GetManufacturingById(string conn, long manufacturingId, CancellationToken cancellationToken = default)
        {
            using (var db = _connectionFactory.Open(KvStatic.GetConnectionName(conn)))
            {
                var itkRepository = new InventoryTrackingRepository(db, _logger);
                return await itkRepository.GetManufacturingById(manufacturingId);
            }
        }

        public async Task<long> GetCountAfterByTransDate(InventoryImpact tracking, CancellationToken cancellationToken = default)
        {
            try
            {
                using (var db = await _connectionFactory.OpenAsync(KvStatic.GetConnectionName(tracking.ConnectionString)))
                {
                    var itkRepository = new InventoryTrackingRepository(db, _logger);
                    return await itkRepository.GetCountAfterByTransDate(tracking.Track);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{ex.Message}");
                return 0;
            }
        }

        #endregion

        #region private
        private async Task<IList<InventoryTracking>> RoundCostAsync(string connectionString, int retailerId ,IList<InventoryTracking> inventoryTrackings)
        {
            if (!inventoryTrackings.Any()) return inventoryTrackings;
            foreach (var inventoryTracking in inventoryTrackings)
            {
                inventoryTracking.Cost = await _multiCurrencyService.Round(connectionString, retailerId, inventoryTracking.Cost, DefaultRoundFunction, isSpecialCase: true);
            }
            return inventoryTrackings;
        }
        #endregion        
    }
}