﻿using System.Collections.Generic;
using KiotViet.Util;

namespace KiotViet.Data.Entity.Pharmacy
{
    public class Login
    {
        public string usr { get; set; }
        public string pwd { get; set; }
    }

    public class TokenData
    {
        public string token { get; set; }
        public string token_type { get; set; }
    }

    public class LoginResponse : BaseResponse
    { 
        public TokenData data { get; set; }
    }

    public class BaseResponse
    {
        public int code { get; set; }
        public string message { get; set; }
        public string mess { get; set; }
    }

    public class Response : BaseResponse
    {        
        public string ma_hoa_don_quoc_gia { get; set; }
        public string ma_phieu_nhap_quoc_gia { get; set; }
        public string ma_phieu_xuat_quoc_gia { get; set; }
    }

    public class InvoiceSync
    {
        public string ma_hoa_don { get; set; }
        public string ma_co_so { get; set; }
        public string ma_don_thuoc_quoc_gia { get; set; }
        public string ngay_ban { get; set; }
        public string ho_ten_nguoi_ban { get; set; }
        public string ho_ten_khach_hang { get; set; }
        public List<InvoiceDetailSync> hoa_don_chi_tiet { get; set; }

        public void Copy(SyncNationalPharmacyEvent syncEvent)
        {
            ma_hoa_don = syncEvent.ma_phieu;
            ma_co_so = syncEvent.ma_co_so;
            ma_don_thuoc_quoc_gia = syncEvent.ma_don_thuoc_quoc_gia;
            ngay_ban = syncEvent.ngay_giao_dich;
            ho_ten_nguoi_ban = syncEvent.ho_ten_nguoi_ban;
            ho_ten_khach_hang = syncEvent.ho_ten_khach_hang;
            hoa_don_chi_tiet = new List<InvoiceDetailSync>();
            foreach (var detail in syncEvent.chi_tiet_obj)
            {
                var temp = new InvoiceDetailSync();
                temp.Copy(detail);
                hoa_don_chi_tiet.Add(temp);
            }
        }
    }

    public class InvoiceDetailSync
    {
        public string ma_thuoc { get; set; }
        public string ten_thuoc { get; set; }
        public string so_lo { get; set; }
        public string ngay_san_xuat { get; set; }
        public string han_dung { get; set; }
        public string don_vi_tinh { get; set; }
        public string ham_luong { get; set; }
        public string duong_dung { get; set; }
        public string lieu_dung { get; set; }
        public string so_dang_ky { get; set; }
        public double so_luong { get; set; }
        public decimal don_gia { get; set; }
        public decimal thanh_tien { get; set; }
        public double ty_le_quy_doi { get; set; }

        public void Copy(SyncNationalPharmacyEventDetail dt)
        {
            ma_thuoc = StringHelper.StandardizedPharmacyData(dt.ma_thuoc);
            ten_thuoc = StringHelper.StandardizedPharmacyData(dt.ten_thuoc);
            so_lo = dt.so_lo;
            ngay_san_xuat = dt.ngay_san_xuat;
            han_dung = StringHelper.StandardizedPharmacyData(dt.han_dung);
            don_vi_tinh = StringHelper.StandardizedPharmacyData(dt.don_vi_tinh);
            ham_luong = StringHelper.StandardizedPharmacyData(dt.ham_luong);
            duong_dung = StringHelper.StandardizedPharmacyData(dt.duong_dung);
            lieu_dung = StringHelper.StandardizedPharmacyData(dt.lieu_dung);
            so_dang_ky = StringHelper.StandardizedPharmacyData(dt.so_dang_ky);
            so_luong = dt.so_luong;
            don_gia = StringHelper.StandardizedPharmacyData(dt.don_gia);
            thanh_tien = dt.thanh_tien;
            ty_le_quy_doi = dt.ty_le_quy_doi;
        }
    }

    public class TransInSync : TransSync
    {
        public string ngay_nhap { get; set; }
        public byte loai_phieu_nhap { get; set; }
        //Tên cơ sở cung cấp thuốc (Nếu là phiếu nhập từ nhà cung cấp).
        public string ten_co_so_cung_cap { get; set; }
        public void Copy(SyncNationalPharmacyEvent syncEvent)
        {
            ma_phieu = syncEvent.ma_phieu;
            ma_co_so = syncEvent.ma_co_so;
            ngay_nhap = syncEvent.ngay_giao_dich;
            loai_phieu_nhap = syncEvent.loai_giao_dich;
            ghi_chu = syncEvent.ghi_chu;
            if (syncEvent.DocumentType == (int)BunchOfEnum.DocumentType.StockTake)
            {
                ghi_chu = "Nhập tồn";
            }
            ten_co_so_cung_cap = syncEvent.ten_co_so_cung_cap_nhan;
            chi_tiet = new List<TransDetailSync>();
            foreach (var detail in syncEvent.chi_tiet_obj)
            {
                var temp = new TransDetailSync();
                temp.Copy(detail);
                chi_tiet.Add(temp);
            }
        }
    }

    public class TransOutSync : TransSync
    {
        public string ngay_xuat { get; set; }
        public byte loai_phieu_xuat { get; set; }
        //Tên cơ sở cung cấp thuốc (Nếu là phiếu tra từ nhà cung cấp).
        public string ten_co_so_nhan { get; set; }
        public void Copy(SyncNationalPharmacyEvent syncEvent)
        {
            ma_phieu = syncEvent.ma_phieu;
            ma_co_so = syncEvent.ma_co_so;
            ngay_xuat = syncEvent.ngay_giao_dich;
            // fix type follow srs api
            if (syncEvent.loai_giao_dich == (byte) BunchOfEnum.PharmacyType.PurchaseReturn)
            {
                loai_phieu_xuat = 2;
            }
            else if (syncEvent.loai_giao_dich == (byte) BunchOfEnum.PharmacyType.StockTransferNegative || syncEvent.loai_giao_dich == (byte)BunchOfEnum.PharmacyType.DamageItem)
            {
                loai_phieu_xuat = 3;
            }
            else
            {
                loai_phieu_xuat = syncEvent.loai_giao_dich;
            }
            ghi_chu = syncEvent.ghi_chu;
            if (syncEvent.DocumentType == (int) BunchOfEnum.DocumentType.StockTake)
            {
                ghi_chu = "Xuất hủy";
            }
            ten_co_so_nhan = syncEvent.ten_co_so_cung_cap_nhan;
            chi_tiet = new List<TransDetailSync>();
            foreach (var detail in syncEvent.chi_tiet_obj)
            {
                var temp = new TransDetailSync();
                temp.Copy(detail);
                chi_tiet.Add(temp);
            }
        }
    }

    public class TransSync
    {
        public string ma_phieu { get; set; }
        public string ma_co_so { get; set; }
        public string ghi_chu { get; set; }
        public List<TransDetailSync> chi_tiet { get; set; }
    }

    public class TransDetailSync
    {
        public string ma_thuoc { get; set; }
        public string ten_thuoc { get; set; }
        public string so_lo { get; set; }
        public string ngay_san_xuat { get; set; }
        public string han_dung { get; set; }
        public string so_dklh { get; set; }
        //Số lượng thuốc quy ra đơn vị tính nhỏ nhất
        public double so_luong { get; set; }
        public decimal don_gia { get; set; }
        //Tên đơn vị tính nhỏ nhất của thuốc
        public string don_vi_tinh { get; set; }
        public void Copy(SyncNationalPharmacyEventDetail dt)
        {
            ma_thuoc = StringHelper.StandardizedPharmacyData(dt.ma_thuoc);
            ten_thuoc = StringHelper.StandardizedPharmacyData(dt.ten_thuoc);
            so_lo = dt.so_lo;
            ngay_san_xuat = dt.ngay_san_xuat;
            han_dung = StringHelper.StandardizedPharmacyData(dt.han_dung);
            so_dklh = StringHelper.StandardizedPharmacyData(dt.so_dklh);
            so_luong = dt.so_luong;
            don_gia = StringHelper.StandardizedPharmacyData(dt.don_gia);
            don_vi_tinh = StringHelper.StandardizedPharmacyData(dt.don_vi_tinh);
        }
    }
}
