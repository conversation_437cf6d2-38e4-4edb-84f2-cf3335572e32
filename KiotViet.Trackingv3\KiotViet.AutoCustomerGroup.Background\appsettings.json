﻿{
  "servicestack:license": "6617-e1JlZjo2NjE3LE5hbWU6Q2l0aWdvIFNvZnR3YXJlIENvLixUeXBlOkluZGllLE1ldGE6MCxIYXNoOkdqM1lUQVhQS05YOWFwYlY3bDA4RWRnc0taWEpoUDdZL0Q0SzZqOENxMXBYQmhLUzVlbDR6aTJXNU1lQzhnWGdTYjA0cDZoMzZuaDZuTE5MbHQ4RG5zUlU3bUpVT0NoejI2a2IvQVVSTzRtYzF4NTkzYTBja1BiZVRMVmFIOU55TFErT1VaQ3NKcm5yd243elNWVDR6ZmkyZUdKUzZNbDFqRkQ2cWNnTEliST0sRXhwaXJ5OjIwMTktMTAtMTZ9",
  "ServiceMode": "console",
  "CustomerGroupBirthdateScanTime": "02:00",
  "CustomerSummaryMissingScanTime": "02:17",
  "CustomerGroupBirthDayScanConns": "KVEntities",
  "CustomerGroupMissScanConns": "KVEntities",
  "Mssql": {
    "AllConnections": [
      {
        "Name": "KVEntities",
        "Value": "Server=dc2d-fnb-mssql-01.citigo.io;Database=PhoenixKiotVietShard1;Persist security info=True;User Id=sa;Password=***************;MultipleActiveResultSets=True;Max Pool Size=10000;",
        "IsDefault": false
      },
      {
        "Name": "KVEntities1",
        "Value": "Server=dc2d-fnb-mssql-01.citigo.io;Database=PhoenixKiotVietShard1;Persist security info=True;User Id=sa;Password=***************;MultipleActiveResultSets=True;Max Pool Size=10000;",
        "IsDefault": false
      },
      {
        "Name": "KVMasterDataEntities",
        "Value": "Server=dc2d-fnb-mssql-01.citigo.io;Database=KiotVietMasterFnBphoenix;Persist security info=True;User Id=sa;Password=***************;MultipleActiveResultSets=True;Max Pool Size=10000;",
        "IsDefault": true
      }
    ]
  },
  "Caching": {
    "Redis": {
      "Servers": "dc2d-fnb-infra-01.citigo.io:26379;dc2d-fnb-infra-02.citigo.io:26379;dc2d-fnb-infra-03.citigo.io:26379",
      "SentinelMasterName": "senkv6379",
      "DbNumber": 8,
      "AuthPass": "",
      "IsSentinel": true,
      "IsStrictPool": false,
      "MaxPoolSize": 400,
      "MaxPoolTimeout": 1000,
      "WaitBeforeForcingMasterFailover": 300
    }
  },
  "AuditTrail": {
    "Redis": {
      "Servers": "dc2d-fnb-infra-01.citigo.io:26380;dc2d-fnb-infra-02.citigo.io:26380;dc2d-fnb-infra-03.citigo.io:26380",
      "SentinelMasterName": "senkv6380",
      "DbNumber": 8,
      "AuthPass": null,
      "IsSentinel": true,
      "IsStrictPool": false,
      "MaxPoolSize": 200,
      "MaxPoolTimeout": 1000,
      "WaitBeforeForcingMasterFailover": 300
    }
  },
  "Kafka": {
    "BootstrapServers": "dc2d-fnb-infra-01.citigo.io:9092,dc2d-fnb-infra-02.citigo.io:9092,dc2d-fnb-infra-03.citigo.io:9092",
    "GroupId": "autoCg-development-local",
    "ThreadSize": 1,
    "EnableAutoCommit": false,
    "AutoOffsetReset": 1,
    "SessionTimeoutMs": 6000,
    "FetchMaxBytes": 104857600,
    "StatisticsIntervalMs": 100000,
    "ClientId": "autoCg-development-local",
    "Topics": [
      "CustomerChangeInfo-phoenix",
      "CustomerGroupChangeInfo-phoenix",
      "CustomerBulkChangeInfo-phoenix"
    ],
    "IntervalTimeCommitBySecond": 1,
    "MessageMaxBytes ": 104857600,
    "UsingDlq": false,
    "MaximumRetryAttempt": 1,
    "RetryThreadSize": 1,
    "EnableHandleDlqTopic": false,
    "ConsumeResultManagerConfig": {
      "GroupId": "autoCg-development",
      "MaximumConcurrentKeyHandle": 100,
      "TimeToWaitBeforeExitWorker": 100
    }
  },
  "RedisProductCacheExpire": 14,
  "Serilog": {
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Warning",
        "System": "Warning"
      }
    },
    "WriteTo": [
      {
        "Name": "File",
        "Args": {
          "path": "log//log.json",
          "rollingInterval": "Day",
          "fileSizeLimitBytes": 10000000,
          "rollOnFileSizeLimit": true,
          "formatter": "Serilog.Formatting.Json.JsonFormatter, Serilog"
        }
      },
      {
        "Name": "Console"
      }
    ]
  }
}
