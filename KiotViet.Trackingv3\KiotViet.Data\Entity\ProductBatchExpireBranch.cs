﻿using System;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class ProductBatchExpireBranch
    {
        [PrimaryKey]
        public long ProductBatchExpireId { get; set; }
        [PrimaryKey]
        public int BranchId { get; set; }
        [PrimaryKey]
        public int RetailerId { get; set; }
        public double OnHand { get; set; }
        public byte Status { get; set; }
        public long? DocumentId { get; set; }
        public int? DocumentType { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public byte[] Revision { get; set; }
    }
}