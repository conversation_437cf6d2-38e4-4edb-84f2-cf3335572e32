﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net6.0</TargetFramework>
		<StartupObject>KiotViet.AutoCustomerGroup.Background.Program</StartupObject>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="KiotVietFnB.Redis" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="6.0.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="6.0.1" />
		<PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.0.0" />
		<PackageReference Include="Serilog.Exceptions" Version="8.3.0" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="3.1.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.3.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\KiotViet.AutoCustomerGroup.Services\KiotViet.AutoCustomerGroup.Services.csproj" />
		<ProjectReference Include="..\KiotViet.Service\KiotViet.Service.csproj" />
		<ProjectReference Include="..\KiotViet.Util\KiotViet.Util.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="appsettings.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
			<ExcludeFromSingleFile>true</ExcludeFromSingleFile>
			<CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
		</Content>
	</ItemGroup>

</Project>
