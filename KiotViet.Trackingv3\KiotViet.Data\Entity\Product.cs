﻿using System.Collections.Generic;
using KiotViet.Data.Interface;
using ServiceStack;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class Product : ILongIdentifiable, IRetailerId
    {
        [PrimaryKey]
        public long Id { get; set; }
        public int RetailerId { get; set; }
        public long? MasterUnitId { get; set; }
        public int ProductType { get; set; }
        public double ConversionValue { get; set; }
        public string ProductChildUnitStr { get; set; }
        public string FormulaStr { get; set; }
        public string MasterFormulaStr { get; set; }

        public bool? InventoryTrackingIgnore { get; set; }
        [Ignore]
        public List<Product> ProductChildUnit { get; set; }
        [Ignore]
        public List<Product> Formula { get; set; }
        [Ignore]
        public List<Product> MasterFormula { get; set; }

        public void Copy(Product product)
        {
            Id = product.Id;
            RetailerId = product.RetailerId;
            MasterUnitId = product.MasterUnitId;
            ProductType = product.ProductType;
            InventoryTrackingIgnore = product.InventoryTrackingIgnore;
            if (!string.IsNullOrEmpty(product.ProductChildUnitStr))
            {
                ProductChildUnit = product.ProductChildUnitStr.FromJson<List<Product>>();
            }
            if (!string.IsNullOrEmpty(product.FormulaStr))
            {
                Formula = product.FormulaStr.FromJson<List<Product>>();
            }
            if (!string.IsNullOrEmpty(product.MasterFormulaStr))
            {
                MasterFormula = product.MasterFormulaStr.FromJson<List<Product>>();
            }
        }

        public static IEnumerable<Product> Convert(IList<Product> products)
        {
            var result = new List<Product>();
            foreach (var product in products)
            {
                if (!string.IsNullOrEmpty(product.ProductChildUnitStr))
                {
                    product.ProductChildUnit = product.ProductChildUnitStr.FromJson<List<Product>>();
                }
                if (!string.IsNullOrEmpty(product.FormulaStr))
                {
                    product.Formula = product.FormulaStr.FromJson<List<Product>>();
                }
                if (!string.IsNullOrEmpty(product.MasterFormulaStr))
                {
                    product.MasterFormula = product.MasterFormulaStr.FromJson<List<Product>>();
                }
                result.Add(product);
            }
            return result;
        }
    }
}
