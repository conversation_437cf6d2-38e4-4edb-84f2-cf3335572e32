﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.Data.Entity;
using KiotViet.Util;
using Microsoft.Extensions.Logging;
using ServiceStack;
using ServiceStack.OrmLite;

namespace KiotViet.Repository.Impl
{
    public class ProductBranchRepository : BaseRepository<ProductBranch>
    {
        public ProductBranchRepository(IDbConnection db, ILogger logger) : base(db)
        {
            _logger = logger;
        }

        public async Task<ProductBranch> GetProductBranchByPrimaryKeyAsync(long productId, int branchId, int retailerId, CancellationToken cancellationToken = default)
        {
            return await Db.SingleAsync(
                Db.From<ProductBranch>().Where(x => x.RetailerId == retailerId && x.BranchId == branchId && x.ProductId == productId),
                cancellationToken);
        }

        public async Task<ProductBranch> GetProductBranchAsync(long productId, int branchId, int retailerId, CancellationToken cancellationToken = default)
        {
            try
            {
                using (var dbTrans = Db.OpenTransaction())
                {
                    var productBranch = await Db.SingleAsync(Db.From<ProductBranch>().Where(x => x.RetailerId == retailerId && x.BranchId == branchId && x.ProductId == productId));
                    if (productBranch != null) return productBranch;
                    productBranch = new ProductBranch { RetailerId = retailerId, BranchId = branchId, ProductId = productId, CreatedDate = DateTime.Now, MinQuantity = 0};
                    await Add(productBranch, cancellationToken);
                    dbTrans.Commit();
                    return productBranch;
                }
            }
            catch (Exception e)
            {
                var logObject = new LogObject(retailerId, branchId);
                var exception = e as SqlException;
                // Violation of primary key. Handle Exception
                if (exception?.Number == 2627)
                {
                    return await Db.SingleAsync(Db.From<ProductBranch>().Where(x => x.RetailerId == retailerId && x.BranchId == branchId && x.ProductId == productId));
                }
                if (exception?.Number != 547)
                {
                    _logger.LogError(e, e.Message);
                }
                return default(ProductBranch);
            }
        }

        private static void CalculateReserved(ProductBranch productBranch, double reservedChange, double onOrderChange, double onTransferChange)
        {
            if (productBranch == null) return;
            productBranch.Reserved += reservedChange;
            productBranch.OnOrder += onOrderChange;
            productBranch.TranferTo += onTransferChange;
        }

        public async Task<Branch> GetBranchNoLock(int id, CancellationToken cancellationToken = default)
        {
            return await Db.SingleAsync<Branch>(@" SELECT TOP(1) Id, Name FROM dbo.[Branch] WITH (NOLOCK) WHERE Id = @id ", new { id }, cancellationToken);
        }

        public async Task<string> GetProductFullNameNoLock(long id, CancellationToken cancellationToken = default)
        {
            return await Db.SqlScalarAsync<string>(@" SELECT TOP(1) FullName FROM dbo.[Product] WITH (NOLOCK) WHERE Id = @id ", new { id }, cancellationToken);
        }

        public async Task UpdateReserved(ProductBranch obj, CancellationToken cancellationToken = default)
        {
            await Db.ExecuteSqlAsync(
                @"
                   UPDATE [dbo].[ProductBranch]
                    SET                         
                        [OnOrder] = @OnOrder
                        ,[TranferTo] = @TranferTo
                        ,[Reserved] = @Reserved
                        ,[ModifiedDate] = GETDATE()
                    WHERE ProductId = @ProductId AND BranchId = @BranchId AND RetailerId = @RetailerId
                ",
                new
                {
                    obj.ProductId,
                    obj.BranchId,
                    obj.RetailerId,
                    obj.OnOrder,
                    obj.TranferTo,
                    obj.Reserved
                },
                cancellationToken);
        }

        public async Task UpdateProductBranchReservedAsync(
            long productId, 
            int branchId, 
            int retailerId,
            double reservedChange, 
            double onOrderChange, 
            double onTransferChange,
            CancellationToken cancellationToken = default)
        {
            try
            {
                using (var dbTrans = Db.OpenTransaction())
                {
                    var productBranch = await Db.SingleAsync(Db.From<ProductBranch>().Where(x => x.RetailerId == retailerId && x.BranchId == branchId && x.ProductId == productId));
                    if (productBranch != null)
                    {
                        CalculateReserved(productBranch, reservedChange, onOrderChange, onTransferChange);
                        await UpdateReserved(productBranch);
                    }
                    else
                    {
                        if (onOrderChange < 0)
                        {
                            onOrderChange = 0;
                        }
                        productBranch = new ProductBranch
                        {
                            RetailerId = retailerId,
                            BranchId = branchId,
                            ProductId = productId,
                            CreatedDate = DateTime.Now,
                            ModifiedDate = DateTime.Now,
                            Reserved = reservedChange,
                            OnOrder = onOrderChange,
                            TranferTo = onTransferChange
                        };
                        await Add(productBranch, cancellationToken);
                    }
                    dbTrans.Commit();
                }
            }
            catch(SqlException e) when (e.Number == 2627) // Violation of primary key. Handle Exception
            {
                _logger.LogError(e, e.Message);
                var productBranch = await Db.SingleAsync(Db.From<ProductBranch>().Where(x => x.RetailerId == retailerId && x.BranchId == branchId && x.ProductId == productId));
                if (productBranch != null)
                {
                    CalculateReserved(productBranch, reservedChange, onOrderChange, onTransferChange);
                    await UpdateReserved(productBranch);
                }
            }
            catch (SqlException e)
            {
                _logger.LogError(e, e.Message);
            }
            catch (Exception e)
            {
                _logger.LogError(e, e.Message);
            }
        }

        public async Task<List<TransDetailShort>> GetComboIdInTransaction(InventoryTracking req, CancellationToken cancellationToken = default)
        {
            List<TransDetailShort> result = null;
            // find all combo in transaction
            List<TransDetailShort> listAllCombo;
            if (req.DocumentType == (int) BunchOfEnum.DocumentType.RefundCombo)
            {
                listAllCombo = await Db.SelectAsync<TransDetailShort>(@"
            SELECT rd.ProductId, rd.Quantity, rd.ProductFormulaHistoryId
            FROM dbo.[Return] AS r INNER JOIN dbo.ReturnDetail AS rd ON rd.ReturnId = r.Id INNER JOIN dbo.Product AS p ON p.Id = rd.ProductId
            WHERE r.Id = @transId AND p.ProductType = 1
            ", new {transId = req.DocumentId});
            }
            else
            {
                listAllCombo = await Db.SelectAsync<TransDetailShort>(@"
            SELECT id.ProductId, id.Quantity, id.ProductFormulaHistoryId 
            FROM dbo.Invoice AS i INNER JOIN dbo.InvoiceDetail AS id ON id.InvoiceId = i.Id INNER JOIN dbo.Product AS p ON p.Id = id.ProductId
            WHERE i.Id = @transId AND p.ProductType = 1
            ", new {transId = req.DocumentId}, cancellationToken);
            }
            if (listAllCombo == null || !listAllCombo.Any()) return null;
            // foreach combo
            foreach (var combo in listAllCombo)
            {
                var material = combo.ProductFormulaHistoryId == null
                    ? Db.Single<string>(@"SELECT MaterialIds FROM dbo.ProductFormulaHistory WHERE ProductId = @productId AND RetailerId = @retailerId ORDER BY CreatedDate DESC"
                        , new {productId = combo.ProductId, retailerId = req.RetailerId})
                    : Db.Single<string>(@"SELECT MaterialIds FROM dbo.ProductFormulaHistory WHERE Id = @id", new {id = combo.ProductFormulaHistoryId});
                // find all materialIds, if exist comboAffected then add combo to processList
                if (string.IsNullOrEmpty(material)) continue;
                var materials = material.FromJson<List<ProductFormulaHistoryItem>>();
                if (!CheckIfChildItemExists(materials, req.ProductId)) continue;
                if(result == null) result = new List<TransDetailShort>();
                result.Add(combo);
            }
            return result;
        }

        public async Task<List<ProductFormulaHistoryItem>> GetAllMaterialsByFormulaHistory(
            int retailerId,
            long transId, 
            long comboId, 
            BunchOfEnum.DocumentType documentType, 
            CancellationToken cancellationToken = default)
        {
            string materials;
            if (documentType == BunchOfEnum.DocumentType.Refund)
            {
                materials = await Db.SingleAsync<string>(@"
            SELECT ISNULL(
            (SELECT TOP 1 p.MaterialIds
            FROM dbo.[ReturnDetail] AS r LEFT JOIN dbo.ProductFormulaHistory AS p ON r.ProductFormulaHistoryId = p.Id
            WHERE r.ReturnId = @transId AND r.ProductId = @comboId ORDER BY r.Id), 
            (SELECT TOP 1 MaterialIds FROM dbo.ProductFormulaHistory WHERE ProductId = @comboId AND RetailerId = @retailerId ORDER BY CreatedDate DESC))
            ", new {transId, comboId, retailerId},
            cancellationToken);
            }
            else
            {
                materials = await Db.SingleAsync<string>(@"
            SELECT ISNULL(
            (SELECT TOP 1 p.MaterialIds
            FROM dbo.InvoiceDetail AS r LEFT JOIN dbo.ProductFormulaHistory AS p ON r.ProductFormulaHistoryId = p.Id
            WHERE r.InvoiceId = @transId AND r.ProductId = @comboId ORDER BY r.Id), 
            (SELECT TOP 1 MaterialIds FROM dbo.ProductFormulaHistory WHERE ProductId = @comboId AND RetailerId = @retailerId ORDER BY CreatedDate DESC))
            ", new {transId, comboId, retailerId}, cancellationToken);
            }
            return materials.FromJson<List<ProductFormulaHistoryItem>>();
        }

        public async Task<List<ProductFormulaHistoryItem>> GetLastestMaterialsByProductId(int retailerId, long comboId, CancellationToken cancellationToken = default)
        {
            var materials = await Db.SingleAsync<string>(@"
            SELECT TOP 1 MaterialIds FROM dbo.ProductFormulaHistory WHERE ProductId = @comboId AND RetailerId = @retailerId ORDER BY CreatedDate DESC
            ", new { comboId, retailerId }, cancellationToken);
            return materials.FromJson<List<ProductFormulaHistoryItem>>();
        }

        public bool CheckIfChildItemExists(IEnumerable<ProductFormulaHistoryItem> childItems, long childItemId)
        {
            if (childItems == null || childItems.Count().Equals(0)) return false;
            foreach (var item in childItems)
            {
                if (item.MaterialId.Equals(childItemId)) return true;
                if (item.ListMaterials == null || !item.ListMaterials.Any()) continue;
                if (CheckIfChildItemExists(item.ListMaterials, childItemId)) return true;
            }
            return false;            
        }

        public async Task Add(ProductBranch obj, CancellationToken cancellationToken = default)
        {
            await Db.ExecuteSqlAsync(
                @"INSERT INTO [dbo].[ProductBranch]
                   ([ProductId]
                   ,[BranchId]
                   ,[RetailerId]
                   ,[OnHand]
                   ,[OnOrder]
                   ,[TranferTo]
                   ,[Reserved]
                   ,[CreatedDate]
                   ,[MinQuantity]
                   ,[MaxQuantity]
                   ,[Cost]
                   ,[LatestPurchasePrice]
                   ,[ModifiedDate])
                     VALUES
                         (
                       @ProductId
                       ,@BranchId
                       ,@RetailerId
                       ,@OnHand
                       ,@OnOrder
                       ,@TranferTo
                       ,@Reserved
                       ,@CreatedDate
                       ,@MinQuantity
                       ,@MaxQuantity
                       ,@Cost
                       ,@LatestPurchasePrice
                       ,@ModifiedDate
                        )
                ",
                new
                {
                    obj.ProductId,
                    obj.BranchId,
                    obj.RetailerId,
                    obj.OnHand,
                    obj.OnOrder,
                    obj.TranferTo,
                    obj.Reserved,
                    obj.CreatedDate,
                    obj.MinQuantity,
                    obj.MaxQuantity,
                    obj.Cost,
                    obj.LatestPurchasePrice,
                    obj.ModifiedDate
                }, 
                cancellationToken);
        }

        public async Task Update(ProductBranch obj, CancellationToken cancellationToken = default)
        {
            // only update onhand, cost, and LatestPurchasePrice
            await Db.ExecuteSqlAsync(
                @"
                   UPDATE [dbo].[ProductBranch]
                    SET 
                        [OnHand] = @OnHand
                        ,[Cost] = @Cost
                        ,[LatestPurchasePrice] = @LatestPurchasePrice
                        ,[ModifiedDate] = GETDATE()
                    WHERE ProductId = @ProductId AND BranchId = @BranchId AND RetailerId = @RetailerId
                ",
                new
                {
                    obj.ProductId,
                    obj.BranchId,
                    obj.RetailerId,
                    obj.OnHand,
                    obj.Cost,
                    obj.LatestPurchasePrice
                },
                cancellationToken);
        }
    }
}