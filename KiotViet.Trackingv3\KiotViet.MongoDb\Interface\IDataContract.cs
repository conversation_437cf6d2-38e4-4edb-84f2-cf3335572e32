﻿using System;
using System.Collections.Generic;
using KiotViet.MongoDb.Entity;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Interface
{
    public interface IEntityId
    {
        ObjectId _id { get; set; }
    }

    public interface ICreatedOn
    {
        DateTime CreatedAt { get; set; }
    }

    public interface IModifiedDate
    {
        DateTime? ModifiedDate { get; set; }
    }

    public interface ITimeMesure
    {
        string TimeMesure { get; set; }
    }

    public interface IHistory
    {
        string History { get; set; }
    }

    public interface IEventType
    {
        string EventType { get; }
    }

    public interface ITotalTime
    {
        double TotalTime { get; set; }
    }

    public interface ITrackingStep
    {
        List<TrackingStep> Steps { get; set; }
    }
}