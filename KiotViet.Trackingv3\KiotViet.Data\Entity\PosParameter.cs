﻿using KiotViet.Data.Interface;

namespace KiotViet.Data.Entity
{
    public class PosParameter: IRetailerId, IIdentifiable
    {
        public int Id { get; set; }
        public string Key { get; set; }
        public string Value { get; set; }
        public int RetailerId { get; set; }
    }

    public class Retailer : IIdentifiable
    {
        public bool UseAvgCost { get; set; }
        public int Id { get; set; }
        public string ConnectionString { get; set; }
        public string Language { get; set; }
        public long AdminId { get; set; }
        public int GroupId { get; set; }
        public string CurrencyCode { get; set; }
        public int? CountryId { get; set; }
    }

    public class RetailerShort
    {
        public int GroupId { get; set; }
        public string ConnectionString { get; set; }
    }
}