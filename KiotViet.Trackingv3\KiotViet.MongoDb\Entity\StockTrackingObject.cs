﻿using System;
using System.Collections.Generic;
using KiotViet.MongoDb.Interface;
using KiotViet.Services.Common;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class StockTrackingObject : IEntityId, ICreatedOn, ITimeMesure, IHistory, IEventType, ITotalTime, ITrackingStep
    {
        //db.StockTrackingObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.StockTrackingObject.createIndex( { "RetailerId": 1, "BranchId": 1, "ProductId": 1 })

        public StockTrackingObject()
        {
            Steps = new List<TrackingStep>();
        }

        public StockTrackingObject(StockImpact source)
        {
            Steps = new List<TrackingStep>();
            if (source == null) return;
            RetailerId = source.RetailerId;
            BranchId = source.BranchId;
            ProductId = source.ProductId;
            OnhandChange = source.OnhandChange;
            ReservedChange = source.ReservedChange;
            TransferToChange = source.TransferToChange;
            OnOrderChange = source.OnOrderChange;
            DocumentId = source.DocumentId;
        }

        public ObjectId _id { get; set; }
        public DateTime CreatedAt { get; set; }
        public string TimeMesure { get; set; }
        public string History { get; set; }

        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        public long ProductId { get; set; }
        public double OnhandChange { get; set; }
        public double ReservedChange { get; set; }
        public double TransferToChange { get; set; }
        public double OnOrderChange { get; set; }
        public long DocumentId { get; set; }
        public string EventType => nameof(StockTrackingObject);
        public double TotalTime { get; set; }
        public List<TrackingStep> Steps { get; set; }
    }
}