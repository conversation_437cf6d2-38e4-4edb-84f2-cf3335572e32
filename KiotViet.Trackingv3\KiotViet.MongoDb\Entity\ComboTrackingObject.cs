﻿using System;
using System.Collections.Generic;
using KiotViet.Data.Entity;
using KiotViet.MongoDb.Interface;
using KiotViet.Services.Common;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class ComboTrackingObject: IEntityId, ICreatedOn, ITimeMesure, IHistory, IEventType, ITotalTime, ITrackingStep
    {
        //db.ComboTrackingObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.ComboTrackingObject.createIndex( { "ListComboTrackings.RetailerId": 1, "ListComboTrackings.DocumentId": 1, "ListComboTrackings.DocumentType": 1 })
        //db.ComboTrackingObject.createIndex( { "ListComboTrackings.RetailerId": 1, "ListComboTrackings.BranchId": 1, "ListComboTrackings.ProductId": 1 })
        //db.ComboTrackingObject.createIndex( { "ListMaterialTrackings.RetailerId": 1, "ListMaterialTrackings.DocumentId": 1, "ListMaterialTrackings.DocumentType": 1 })
        //db.ComboTrackingObject.createIndex( { "ListMaterialTrackings.RetailerId": 1, "ListMaterialTrackings.BranchId": 1, "ListMaterialTrackings.ProductId": 1 })
        public ComboTrackingObject()
        {
            Steps = new List<TrackingStep>();
        }

        public ComboTrackingObject(IEnumerable<InventoryImpact> listComboTrackings, IEnumerable<InventoryImpact> listMaterialTrackings)
        {
            Steps = new List<TrackingStep>();
            ListComboTrackings = new List<InventoryTracking>();
            ListMaterialTrackings = new List<InventoryTracking>();
            if (listComboTrackings != null)
            {
                foreach (var comboTracking in listComboTrackings)
                {
                    ListComboTrackings.Add(comboTracking.Track);
                }
            }
            if (listMaterialTrackings != null)
            {
                foreach (var materialTracking in listMaterialTrackings)
                {
                    ListMaterialTrackings.Add(materialTracking.Track);
                }
            }
        }

        public List<InventoryTracking> ListComboTrackings { get; set; }
        public List<InventoryTracking> ListMaterialTrackings { get; set; }

        public ObjectId _id { get; set; }
        public DateTime CreatedAt { get; set; }
        public string TimeMesure { get; set; }
        public string History { get; set; }
        public string EventType => nameof(ComboTrackingObject);
        public double TotalTime { get; set; }
        public List<TrackingStep> Steps { get; set; }
    }
}