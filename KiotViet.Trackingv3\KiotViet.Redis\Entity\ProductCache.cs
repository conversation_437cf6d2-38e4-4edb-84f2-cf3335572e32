﻿using System;
using System.Collections.Generic;
using System.Linq;
using KiotViet.Data.Entity;
using KiotViet.Data.Interface;
using KiotViet.Redis.Interface;

namespace KiotViet.Redis.Entity
{
    public class ProductCache : IRetailerId, ICreatedDate, IEntityId
    {
        private object _id;
        public object Id
        {
            get => _id ?? (_id = $"{RetailerId}:{ProductId}");
            set => _id = value;
        }
        public int RetailerId { get; set; }
        public DateTime CreatedDate { get; set; }
        public long ProductId { get; set; }
        public object MasterUnitIdOnRedis => MasterUnitId != null ? $"{RetailerId}:{MasterUnitId}" : null;
        public long? MasterUnitId { get; set; }
        public int ProductType { get; set; }
        public double ConversionValue { get; set; }
        public List<ProductCache> ProductChildUnit { get; set; }
        public List<ProductCache> Formula { get; set; }
        public List<ProductCache> MasterFormula { get; set; }

        public Product Convert()
        {
            var result = new Product
            {
                Id = ProductId,
                RetailerId = RetailerId,
                MasterUnitId = MasterUnitId,
                ProductType = ProductType,
                ConversionValue = ConversionValue
            };
            if (ProductChildUnit != null && ProductChildUnit.Count > 0)
            {
                result.ProductChildUnit = new List<Product>();
                foreach (var pro in ProductChildUnit)
                {
                    result.ProductChildUnit.Add(new Product
                    {
                        Id = pro.ProductId,
                        RetailerId = pro.RetailerId,
                        ProductType = pro.ProductType,
                        MasterUnitId =  pro.MasterUnitId,
                        ConversionValue = pro.ConversionValue
                    });
                }
            }
            if (Formula != null && Formula.Count > 0)
            {
                result.Formula = new List<Product>();
                foreach (var pro in Formula)
                {
                    result.Formula.Add(new Product
                    {
                        Id = pro.ProductId,
                        RetailerId = pro.RetailerId,
                        ProductType = pro.ProductType,
                        MasterUnitId = pro.MasterUnitId,
                        ConversionValue = pro.ConversionValue
                    });
                }
            }
            if (MasterFormula != null && MasterFormula.Count > 0)
            {
                result.MasterFormula = new List<Product>();
                foreach (var pro in MasterFormula)
                {
                    result.MasterFormula.Add(new Product
                    {
                        Id = pro.ProductId,
                        RetailerId = pro.RetailerId,
                        ProductType = pro.ProductType,
                        MasterUnitId = pro.MasterUnitId,
                        ConversionValue = pro.ConversionValue
                    });
                }
            }
            return result;
        }
    }
}
