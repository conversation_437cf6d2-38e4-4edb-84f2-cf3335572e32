﻿using KiotViet.Data.Entity;
using KiotViet.Redis.Interface;

namespace KiotViet.Redis.Entity
{
    public class RetailerCache : Retailer, IEntityId
    {
        public new object Id { get; set; }
        public int RetailerId { get; set; }

        public void Copy(Retailer r)
        {
            RetailerId = r.Id;
            UseAvgCost = r.UseAvgCost;
            ConnectionString = r.ConnectionString;
            GroupId = r.GroupId;
            AdminId = r.AdminId;
        }
    }
}