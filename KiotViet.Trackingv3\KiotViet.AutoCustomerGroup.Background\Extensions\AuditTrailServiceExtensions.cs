﻿using KiotVietFnB.Redis.Configuration;
using KiotVietFnB.Redis.Helpers;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using ServiceStack.Messaging;

namespace KiotViet.AutoCustomerGroup.Background.Extensions
{
    public static class AuditTrailServiceExtensions
    {
        private const string _auditRedisConfigSection = "AuditTrail:Redis";
        public static void AddAuditTrailService(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddSingleton<IMessageFactory>((sp) =>
            {
                var config = new DefaultRedisConfiguration();
                configuration.GetSection(_auditRedisConfigSection).Bind(config);
                var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
                var logger = loggerFactory.CreateLogger(typeof(RedisMessageFactory));
                var clientManager = RedisHelpers.GetClientsManager(config, logger);
                return new RedisMessageFactory(clientManager);
            });
        }
    }
}
