﻿using Confluent.Kafka;
using KiotVietFnB.Kafka.AsyncConsumer.Interfaces;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.Data.Mongo;
using Serilog.Context;
using KiotViet.Util;
using KiotViet.AutoCustomerGroup.Services.Constants;

namespace KiotViet.AutoCustomerGroup.Background.CustomerGroupConsumers
{
    public class CustomerGroupConsumerHandler : IConsumeHandler<string, string>
    {
        private readonly ILogger<CustomerGroupConsumerHandler> _logger;
        private readonly IServiceScopeFactory _serviceScopeFactory;
        public CustomerGroupConsumerHandler
            (ILogger<CustomerGroupConsumerHandler> logger
            , IServiceScopeFactory serviceScopeFactory)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
        }
        public async Task HandleAsync(ConsumeResult<string, string> message, CancellationToken cancellationToken)
        {
            _logger.LogInformation("Starting Handle Message...");
            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var topicName = message.Topic;
                var modelName = $"{topicName.Split("-")[0]}";

                Type modelType;
                if (!Constants.ModelType.TryGetValue(modelName, out modelType))
                    throw new KvException($"Don't have this modelType {modelName}.");

                using (LogContext.PushProperty("Service", topicName))
                using (LogContext.PushProperty("TenantId", message.Key))
                    try
                    {
                        var concreteType = typeof(ICustomerGroupHandler<>).MakeGenericType(modelType);
                        var serviceHandle = scope.ServiceProvider.GetService(concreteType);
                        var method = serviceHandle?.GetType().GetMethod(nameof(ICustomerGroupHandler<KvContext>.ProcessMessage));
                        if (method != null)
                        {
                             await (Task)method.Invoke(serviceHandle, new object[] { message });
                            _logger.LogInformation("Handled succesfully!");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, ex.Message);
                    }
            }
        }
    }
}
