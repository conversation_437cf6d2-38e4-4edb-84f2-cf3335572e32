﻿using Confluent.Kafka;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.AutoCustomerGroup.Services.Model;
using ServiceStack;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;

namespace KiotViet.AutoCustomerGroup.Services.Impl
{
    public class CustomerChangeInfoHandler : ICustomerGroupHandler<CustomerChangeInfo>
    {
        private readonly IAutoCgService _AutocgService;
        public CustomerChangeInfoHandler(IAutoCgService autoCgService)
        {
            _AutocgService = autoCgService ?? throw new ArgumentNullException(nameof(autoCgService));
        }
        public async Task ProcessMessage(ConsumeResult<string, string> message)
        {
            var customerInfo = message.Value.FromJson<CustomerChangeInfo>();
            await _AutocgService.ProcessCustomerInfoChange(customerInfo);
        }
    }
}
