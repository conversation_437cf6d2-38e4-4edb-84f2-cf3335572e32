﻿using System;

namespace KiotViet.Data.Recovery
{
    public class RecoveryTracking
    {
        public string ConnectionString { get; set; }
        public string FolderName { get; set; }
        public string FileName { get; set; }
        public string Case { get; set; }
        public long TotalDetected { get; set; }
        public long TotalSuccess { get; set; }
        public string SuccessRecors { get; set; }
        public long TotalFailed { get; set; }
        public string FailedRecors { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class MissingResult
    {
        public long Id { get; set; }
        public string RetailerCode { get; set; }
    }

    public class CashFlowResult : MissingResult
    {
        public int DocumentType { get; set; }
    }
}
