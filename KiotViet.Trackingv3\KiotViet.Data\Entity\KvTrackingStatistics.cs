﻿using System;

namespace KiotViet.Data.Entity
{
    public class KvTrackingStatistics
    {
        public long Id { get; set; }
        public string Topic { get; set; }
        public int Partition { get; set; }
        public string ConsumerName { get; set; }
        public string ConsumerId { get; set; }
        public DateTime AssignDate { get; set; }
        public DateTime UnAssignDate { get; set; }
        public long CurNextOffset { get; set; }
        public long CurCommittedOffset { get; set; }
        public long CurUnCommittedOffset { get; set; }
        public long LastNextOffset { get; set; }
        public long LastCommittedOffset { get; set; }
        public long LastUnCommittedOffset { get; set; }
        public bool? IsStable { get; set; }
    }
}