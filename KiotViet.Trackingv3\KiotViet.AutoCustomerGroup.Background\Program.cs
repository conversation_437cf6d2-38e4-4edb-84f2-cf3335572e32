﻿using System;
using System.Configuration;
using System.IO;
using KiotViet.Util;
using KiotViet.Util.Control;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Exceptions;
using ServiceStack;

namespace KiotViet.AutoCustomerGroup.Background
{
    static class Program
    {
        public static readonly string Namespace = typeof(Program).Namespace;
        public static readonly string AppName = Namespace.Substring(Namespace.LastIndexOf('.', Namespace.LastIndexOf('.') - 1) + 1);

        public static void Main(string[] args)
        {
            var webHost = CreateWebHostBuilder(args).Build();
            webHost.Run();
        }

        private static IWebHostBuilder CreateWebHostBuilder(string[] args)
        {
            var config = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddEnvironmentVariables()
            .Build();

            Log.Logger = CreateSerilogLogger(config);

            KvAppConfig.KvTrackingConfigurationBuilder = config;
            var mssql = new Mssql();
            KvAppConfig.KvTrackingConfigurationBuilder.GetSection("Mssql").Bind(mssql);
            KvStatic.InitConnectionStringDictionary(mssql);

            var kafka = new Kafka();
            KvAppConfig.KvTrackingConfigurationBuilder.GetSection("Kafka").Bind(kafka);
            KafkaControl.Instance.SetKafkaConfig(kafka);

            var licenseServicestack = config.GetSection("servicestack:license");
            if (licenseServicestack != null && !string.IsNullOrEmpty(licenseServicestack.Value))
            {
                Licensing.RegisterLicense(licenseServicestack.Value);
            }

            return WebHost.CreateDefaultBuilder(args)
                .ConfigureAppConfiguration(x => x.AddConfiguration(config))
                .UseContentRoot(Directory.GetCurrentDirectory())
                .UseSerilog()
                .SuppressStatusMessages(true)
                .UseStartup<Startup>();
        }

        private static ILogger CreateSerilogLogger(IConfiguration configuration)
        {
            return new LoggerConfiguration()
                .MinimumLevel.Verbose()
                .Enrich.WithProperty("ApplicationContext", AppName)
                .Enrich.WithProperty("MachineName", Environment.MachineName)
                .Enrich.WithExceptionDetails()
                .Enrich.FromLogContext()
                .ReadFrom.Configuration(configuration)
                .CreateLogger();
        }

    }
}
