﻿using System.Data;
using System.Threading.Tasks;
using KiotViet.Data.Entity;
using ServiceStack.OrmLite.Dapper;

namespace KiotViet.Repository.Impl
{
    public class UserRepository : BaseRepository<User>
    {
        public UserRepository(IDbConnection db) : base(db)
        {
        }

        public async Task<User> GetAdminByRetailerId(int retailerId)
        {
            var sql = @"
SELECT u.Id ,
       u.RetailerId
FROM   dbo.[User] AS u
WHERE  u.RetailerId = @retailerId
       AND u.IsAdmin = 1;
";
            return await Db.QueryFirstAsync<User>(sql, new {retailerId});
        }

    }
}