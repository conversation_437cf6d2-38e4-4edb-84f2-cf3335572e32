﻿using System.Data;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.Data.Entity;
using KiotViet.Util;
using ServiceStack.OrmLite;

namespace KiotViet.Repository.Impl
{
    public class PosParameterRepository : BaseRepository<PosParameter>
    {
        public PosParameterRepository(IDbConnection db) : base(db)
        {
        }
        
        public async Task<Retailer> GetParamUseAvgCost(int retailerId, CancellationToken cancellationToken = default)
        {
            var posParam = await GetParamByRetailerIdAndKey(retailerId, "UseAvgCost", cancellationToken);
            return posParam == null
                ? new Retailer {UseAvgCost = true, Id = retailerId}
                : new Retailer {UseAvgCost = ConvertHelper.ToBoolean(posParam.Value), Id = posParam.RetailerId};
        }

        public async Task<PosParameter> GetParamByRetailerIdAndKey(int retailerId, string key, CancellationToken cancellationToken = default)
        {
            return await Db.SingleAsync<PosParameter>(x => x.RetailerId == retailerId && x.Key == key, cancellationToken);
        }

        public async Task<RetailerShort> GetConnectionString(int retailerId, CancellationToken cancellationToken = default)
        {
            var conn = await Db.SingleAsync<RetailerShort>(@"
            SELECT TOP (1) r.GroupId, g.ConnectionString
                FROM dbo.KvRetailer AS r INNER JOIN dbo.KvGroup AS g ON g.Id = r.GroupId
                WHERE r.Id = @retailerId
            ", new { retailerId }, cancellationToken);
            if (!string.IsNullOrEmpty(conn.ConnectionString))
            {
                conn.ConnectionString = conn.ConnectionString.Replace("name=", "");
            }
            return conn;
        }
        
        public Task Add(PosParameter obj)
        {
            throw new System.NotImplementedException();
        }

        public Task Update(PosParameter obj)
        {
            throw new System.NotImplementedException();
        }
    }
}