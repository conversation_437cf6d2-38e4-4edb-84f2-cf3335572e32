﻿using System;
using System.Collections.Generic;
using System.Linq;
using KiotViet.Data.Interface;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class ProductBranch : IRetailerId
    {
        [PrimaryKey]
        public long ProductId { get; set; }
        [PrimaryKey]
        public int BranchId { get; set; }
        [PrimaryKey]
        public int RetailerId { get; set; }
        public double OnHand { get; set; }
        public double OnOrder { get; set; }
        public double TranferTo { get; set; }
        public double Reserved { get; set; }
        public DateTime CreatedDate { get; set; }
        public double MinQuantity { get; set; }
        public double MaxQuantity { get; set; }
        public decimal Cost { get; set; }
        public decimal LatestPurchasePrice { get; set; }
        public DateTime? ModifiedDate { get; set; }
    }

    public class ProductFormulaHistoryItem
    {
        public long ProductId { get; set; }
        public long MaterialId { get; set; }
        public float Quantity { get; set; }
        public int ProductType { get; set; }
        public IEnumerable<ProductFormulaHistoryItem> ListMaterials { get; set; }
        public IEnumerable<ProductFormulaHistoryItem> GetNodeAndDescendants() // Note that this method is lazy
        {
            return new[] { this }
                .Concat(ListMaterials.SelectMany(child => child.GetNodeAndDescendants()));
        }
    }
}