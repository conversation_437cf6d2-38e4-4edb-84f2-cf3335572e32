﻿using System;
using System.Collections.Generic;
using KiotViet.Data.Entity;
using KiotViet.MongoDb.Interface;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class InventoryTrackingObject : InventoryTracking, IEntityId, ICreatedOn, ITimeMesure, IHistory, IEventType, ITrackingStep, ITotalTime
    {
        //db.InventoryTrackingObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.InventoryTrackingObject.createIndex( { "RetailerId": 1, "DocumentId": 1, "DocumentType": 1 })
        //db.InventoryTrackingObject.createIndex( { "RetailerId": 1, "BranchId": 1, "ProductId": 1 })
        public InventoryTrackingObject()
        {
            Steps = new List<TrackingStep>();
        }

        public InventoryTrackingObject(InventoryTracking o)
        {
            Steps = new List<TrackingStep>();
            if (o == null) return;
            CopyFrom(o);
        }

        public DateTime CreatedAt { get; set; }
        public string TimeMesure { get; set; }
        public string History { get; set; }
        public ObjectId _id { get; set; }
        public string EventType => nameof(InventoryTracking);
        public List<TrackingStep> Steps { get; set; }
        public double TotalTime { get; set; }
    }
}