﻿using System;
using System.Collections.Generic;
using KiotViet.Data.Interface;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class InventoryTracking : IRetailerId
    {
        public InventoryTracking() { }
        public InventoryTracking(InventoryTracking source) : this()
        {
            CopyFrom(source);
        }
        [PrimaryKey]
        public long ProductId { get; set; }
        public double Quantity { get; set; }
        public double EndingStocks { get; set; }
        public string DocumentCode { get; set; }
        [PrimaryKey]
        public long DocumentId { get; set; }
        [PrimaryKey]
        public int DocumentType { get; set; }
        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        public decimal Price { get; set; }
        public decimal Cost { get; set; }
        public DateTime TransDate { get; set; }
        [Ignore]
        public DateTime? OriginalTransDate { get; set; }
        public decimal HiddenCost { get; set; }
        public bool IsSpecial { get; set; }
        [IgnoreAttribute]
        public byte? ProductType { get; set; }
        [IgnoreAttribute]
        public bool MainCombo { get; set; }
        [IgnoreAttribute]
        public IList<InventoryTracking> MainComboTracking { get; set; }
        [IgnoreAttribute]
        public DateTime TransDateProcess { get; set; }
        [IgnoreAttribute]
        public Guid KeyBig { get; set; }
        public void CopyFrom(InventoryTracking source)
        {
            ProductId = source.ProductId;
            Quantity = source.Quantity;
            EndingStocks = source.EndingStocks;
            DocumentCode = source.DocumentCode;
            DocumentType = source.DocumentType;
            BranchId = source.BranchId;
            RetailerId = source.RetailerId;
            Price = source.Price;
            Cost = source.Cost;
            TransDate = source.TransDate;
            DocumentId = source.DocumentId;
            ProductType = source.ProductType;
            MainCombo = source.MainCombo;
            MainComboTracking = source.MainComboTracking;
            TransDateProcess = source.TransDateProcess;
        }
    }
    public class DummyInventoryTracking : InventoryTracking
    {
    }
    public class TransDetailShort
    {
        public long ProductId { get; set; }
        public float Quantity { get; set; }
        public int? ProductFormulaHistoryId { get; set; }
    }
    public class InventoryShort
    {
        public long ProductId { get; set; }
        public int Quantity { get; set; }
    }
}
