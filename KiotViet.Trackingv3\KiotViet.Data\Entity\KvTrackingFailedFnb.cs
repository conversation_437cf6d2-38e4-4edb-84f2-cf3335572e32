﻿using System;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class KvTrackingFailedFnb
    {
        [PrimaryKey]
        public int Id { get; set; }
        public string Guid { get; set; }
        public string Topic { get; set; }
        public string Key { get; set; }
        public string Data { get; set; }
        public DateTime MsgDate { get; set; }
        public byte Status { get; set; }
        public int RetailerId { get; set; }
        public string HistoryStatus { get; set; }
        public int RetryTimes { get; set; }
        public bool IsLock { get; set; }
    }
}