﻿using KiotViet.Data.Interface;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class Return: ILongIdentifiable
    {
        [PrimaryKey]
        public long Id { get; set; }
        public long? InvoiceId { get; set; }
        [Ignore]
        public decimal InvoiceCost { get; set; }
        [Ignore]
        public bool IsFound { get; set; }
        [Ignore]
        public bool IsHaveTracking { get; set; }
    }
}