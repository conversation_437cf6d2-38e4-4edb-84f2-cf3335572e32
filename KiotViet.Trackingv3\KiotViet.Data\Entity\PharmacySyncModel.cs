﻿using System.Collections.Generic;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class SyncNationalPharmacyEvent
    {
        [PrimaryKey]
        public long Id { get; set; }
        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        public long DocumentId { get; set; }
        public int DocumentType { get; set; }
        public string ConnectionString { get; set; }
        public byte Status { get; set; }
        public string History { get; set; }
        public int RetryTimes { get; set; }
        public byte Action { get; set; }
        public string ma_phieu { get; set; }
        public string ma_phieu_quoc_gia { get; set; }
        public string ma_co_so { get; set; }
        public string ma_don_thuoc_quoc_gia { get; set; }
        public string ngay_giao_dich { get; set; }
        public byte loai_giao_dich { get; set; }
        public string ho_ten_nguoi_ban { get; set; }
        public string ho_ten_khach_hang { get; set; }
        public string ghi_chu { get; set; }
        public string ten_co_so_cung_cap_nhan { get; set; }
        public string chi_tiet { get; set; }
        public List<SyncNationalPharmacyEventDetail> chi_tiet_obj { get; set; }
        public long EventCheckSum { get; set; }

        public SyncNationalPharmacyEvent(IEnumerable<SyncNationalPharmacyEventDetail> chitietobj, int retailerId, int branchId, long documentId, int documentType, string connectionString, byte status, string history, int retryTimes, byte action, string maphieu, string maphieuquocgia, string macoso, string madonthuocquocgia, string ngaygiaodich, byte loaigiaodich, string hotennguoiban, string hotenkhachhang, string ghichu, string tencosocungcapnhan, string chitiet)
        {
            RetailerId = retailerId;
            BranchId = branchId;
            DocumentId = documentId;
            DocumentType = documentType;
            ConnectionString = connectionString;
            Status = status;
            History = history;
            RetryTimes = retryTimes;
            Action = action;
            ma_phieu = maphieu;
            ma_phieu_quoc_gia = maphieuquocgia;
            ma_co_so = macoso;
            ma_don_thuoc_quoc_gia = madonthuocquocgia;
            ngay_giao_dich = ngaygiaodich;
            loai_giao_dich = loaigiaodich;
            ho_ten_nguoi_ban = hotennguoiban;
            ho_ten_khach_hang = hotenkhachhang;
            ghi_chu = ghichu;
            ten_co_so_cung_cap_nhan = tencosocungcapnhan;
            chi_tiet = chitiet;
            if (chi_tiet_obj == null)
            {
                chi_tiet_obj = new List<SyncNationalPharmacyEventDetail>();
            }
            foreach (var detail in chitietobj)
            {
                var temp = new SyncNationalPharmacyEventDetail(detail.ma_thuoc, detail.ten_thuoc, detail.so_lo,
                    detail.ngay_san_xuat, detail.han_dung, detail.don_vi_tinh, detail.ham_luong, detail.duong_dung,
                    detail.lieu_dung, detail.so_dang_ky, detail.so_luong, detail.don_gia, detail.thanh_tien,
                    detail.ty_le_quy_doi, detail.so_dklh);
                chi_tiet_obj.Add(temp);
            }
        }
    }

    public class SyncNationalPharmacyEventDetail
    {
        public string ma_thuoc { get; set; }
        public string ten_thuoc { get; set; }
        public string so_lo { get; set; }
        public string ngay_san_xuat { get; set; }
        public string han_dung { get; set; }
        public string don_vi_tinh { get; set; }
        public string ham_luong { get; set; }
        public string duong_dung { get; set; }
        public string lieu_dung { get; set; }
        public string so_dang_ky { get; set; }
        public double so_luong { get; set; }
        public decimal don_gia { get; set; }
        public decimal thanh_tien { get; set; }
        public double ty_le_quy_doi { get; set; }
        public string so_dklh { get; set; }

        public SyncNationalPharmacyEventDetail(string mathuoc, string tenthuoc, string solo, string ngaysanxuat,
            string handung, string donvitinh, string hamluong, string duongdung, string lieudung, string sodangky,
            double soluong, decimal dongia, decimal thanhtien, double tylequydoi, string sodklh)
        {
            ma_thuoc = mathuoc;
            ten_thuoc = tenthuoc;
            so_lo = solo;
            ngay_san_xuat = ngaysanxuat;
            han_dung = handung;
            don_vi_tinh = donvitinh;
            ham_luong = hamluong;
            duong_dung = duongdung;
            lieu_dung = lieudung;
            so_dang_ky = sodangky;
            so_luong = soluong;
            don_gia = dongia;
            thanh_tien = thanhtien;
            ty_le_quy_doi = tylequydoi;
            so_dklh = sodklh;
        }
    }
}
