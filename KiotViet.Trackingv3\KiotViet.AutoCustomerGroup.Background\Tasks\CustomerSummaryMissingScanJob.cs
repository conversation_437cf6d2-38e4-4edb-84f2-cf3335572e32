﻿using System;
using KiotViet.Util;
using System.Threading.Tasks;
using KiotViet.AutoCustomerGroup.Services.Model;
using Microsoft.Extensions.Logging;
using KiotViet.AutoCustomerGroup.Services.Interface;

namespace KiotViet.AutoCustomerGroup.Background.Tasks
{
    public class CustomerSummaryMissingScanJob : BaseDailyJob<CustomerSummaryMissingScanJob>
    {
        private readonly IAutoCgService _autoCgService;

        protected override string TimeExecuteConfigKey { get; set; } = "CustomerSummaryMissingScanTime";

        public CustomerSummaryMissingScanJob(ILogger<CustomerSummaryMissingScanJob> logger, IAutoCgService autoCgService) : base(logger)
        {
            _autoCgService = autoCgService;
        }


        [System.Diagnostics.CodeAnalysis.SuppressMessage("Usage", "CA2254:Template should be a static expression", Justification = "<Pending>")]
        protected override async Task ExecuteJob(DateTime time)
        {
            var connectionConfig = KvAppConfig.KvTrackingConfigurationBuilder["CustomerGroupMissScanConns"];
            if (string.IsNullOrEmpty(connectionConfig))
            {
                return;
            }

            var connectionStrings = connectionConfig.Split(",");
            foreach (var connection in connectionStrings)
            {
                try
                {
                    Logger.LogWarning($"Processing missing problem on {connection}");
                    var scanRequest = new MissingInfoScanRequest
                    {
                        TimeSpan = TimeSpan.FromDays(1),
                        ConnectionString = connection,
                    };
                    var missingCount = await _autoCgService.UpdateMissingCustomerSummaryInfo(scanRequest, error =>
                    {
                        Logger.LogError(error);
                    });
                    Logger.LogWarning($"Processed {missingCount} problem on {connection}");
                }
                catch (Exception e)
                {
                    Logger.LogError(e.ToString());
                }
            }
        }
    }
}