﻿using System.Collections.Generic;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.Data.Entity;
using ServiceStack;
using ServiceStack.OrmLite;

namespace KiotViet.Repository.Impl
{
    public class ProductRepository : BaseRepository<Product>
    {
        public ProductRepository(IDbConnection db) : base(db)
        {
        }

        public async Task<Product> GetShortProductById(long productId, CancellationToken cancellationToken = default)
        {
            var product = await Db.SingleAsync<Product>(@"SELECT  Id ,
            RetailerId ,
            MasterUnitId ,
            ProductType ,
            InventoryTrackingIgnore,
            ConversionValue ,
            ( SELECT    p2.Id, p2.RetailerId, p2.MasterUnitId, p2.ProductType, p2.ConversionValue
              FROM      [Product] AS p1 WITH ( NOLOCK )
                        INNER JOIN dbo.[Product] AS p2 WITH ( NOLOCK ) ON p2.MasterUnitId = p1.Id
              WHERE     p1.Id = p.Id
            FOR
              JSON PATH
            ) AS ProductChildUnitStr,
		    ( SELECT    p3.Id, p3.RetailerId, p3.MasterUnitId, p3.ProductType, p3.ConversionValue 
              FROM      [Product] AS p1 WITH ( NOLOCK )
                        INNER JOIN dbo.ProductFormula AS p2 WITH ( NOLOCK ) ON p1.Id = p2.ProductId
					    INNER JOIN dbo.[Product] AS p3 WITH ( NOLOCK ) ON p2.MaterialId = p3.Id
              WHERE     p1.Id = p.Id
            FOR
              JSON PATH
            ) AS FormulaStr,
		    ( SELECT    p3.Id, p3.RetailerId, p3.MasterUnitId, p3.ProductType, p3.ConversionValue 
              FROM      [Product] AS p1 WITH ( NOLOCK )
                        INNER JOIN dbo.ProductFormula AS p2 WITH ( NOLOCK ) ON p1.Id = p2.MaterialId
					    INNER JOIN dbo.[Product] AS p3 WITH ( NOLOCK ) ON p2.ProductId = p3.Id
              WHERE     p1.Id = p.Id
            FOR
              JSON PATH
            ) AS MasterFormulaStr
            FROM    [Product] AS p WITH ( NOLOCK )
            WHERE   p.Id = @id AND p.MasterUnitId IS NULL", new { id = productId }, cancellationToken);
            if (product == null) return null;
            var result = new Product();
            result.Copy(product);
            return result;
        }
        public IEnumerable<Product> GetShortProductById(IEnumerable<long> productIds)
        {
            var listIds = productIds.Join(",");
            var products = Db.SqlList<Product>(@"    
            DECLARE @temp TABLE ( proId BIGINT PRIMARY KEY );
            INSERT  @temp
            ( proId
            )
            SELECT  *
            FROM    [dbo].[fnSplitString](@listIds, ',');
            SELECT  Id ,
            RetailerId ,
            MasterUnitId ,
            ProductType ,
            InventoryTrackingIgnore,
            ConversionValue ,
            ( SELECT    p2.Id, p2.RetailerId, p2.MasterUnitId, p2.ProductType, p2.ConversionValue
              FROM      [Product] AS p1 WITH ( NOLOCK )
                        INNER JOIN dbo.[Product] AS p2 WITH ( NOLOCK ) ON p2.MasterUnitId = p1.Id
              WHERE     p1.Id = p.Id
            FOR
              JSON PATH
            ) AS ProductChildUnitStr,
		    ( SELECT    p3.Id, p3.RetailerId, p3.MasterUnitId, p3.ProductType, p3.ConversionValue 
              FROM      [Product] AS p1 WITH ( NOLOCK )
                        INNER JOIN dbo.ProductFormula AS p2 WITH ( NOLOCK ) ON p1.Id = p2.ProductId
					    INNER JOIN dbo.[Product] AS p3 WITH ( NOLOCK ) ON p2.MaterialId = p3.Id
              WHERE     p1.Id = p.Id
            FOR
              JSON PATH
            ) AS FormulaStr,
		    ( SELECT    p3.Id, p3.RetailerId, p3.MasterUnitId, p3.ProductType, p3.ConversionValue 
              FROM      [Product] AS p1 WITH ( NOLOCK )
                        INNER JOIN dbo.ProductFormula AS p2 WITH ( NOLOCK ) ON p1.Id = p2.MaterialId
					    INNER JOIN dbo.[Product] AS p3 WITH ( NOLOCK ) ON p2.ProductId = p3.Id
              WHERE     p1.Id = p.Id
            FOR
              JSON PATH
            ) AS MasterFormulaStr
            FROM    [Product] AS p WITH ( NOLOCK ) INNER JOIN @temp AS t ON t.proId = p.Id
            WHERE   p.MasterUnitId IS NULL", new { listIds });
            return Product.Convert(products);
        }
    }
}