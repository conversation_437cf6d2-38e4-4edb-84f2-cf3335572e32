﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using KiotViet.Audit.Model.Entity;
using KiotViet.AutoCustomerGroup.Services.Interface;
using KiotViet.AutoCustomerGroup.Services.Model;
using KiotViet.Data.Entity;
using KiotViet.Data.Mongo;
using KiotViet.Redis.Entity;
using KiotViet.Service.Helper;
using KiotViet.Util.CustomerGroupHelper;
using KiotVietFnB.Abstractions.Caching;
using ServiceStack;
using static KiotViet.Util.CustomerGroupHelper.CustomerGroupUpdateType;


namespace KiotViet.AutoCustomerGroup.Services.Impl
{
    public class AutoCgService : IAutoCgService
    {
        private readonly ICustomerGroupService _customerGroupService;
        private readonly IRetailerService _retailerService;
        private readonly ICacheClient _cacheClient;

        public AutoCgService(
            ICustomerGroupService customerGroupService, 
            IRetailerService retailerService,
            ICacheClient cacheClient)
        {
            _customerGroupService = customerGroupService;
            _retailerService = retailerService;
            _cacheClient = cacheClient;
        }

        public async Task ProcessCustomerInfoChange(CustomerChangeInfo customerInfo)
        {
            Guard(customerInfo);

            var filter = await GetAllCustomerGroupFiltersByRetailerIdAsync(customerInfo.ConnectionString, customerInfo.Customer.RetailerId);
            if (filter?.GroupFilters == null || !filter.GroupFilters.Any())
            {
                return;
            }
            var customer = customerInfo.Customer;
            await _customerGroupService.AutoChangeCustomerGroupAsync(customerInfo, customer, filter.GroupFilters);
        }

        private async Task<CustomerGroupFilterCache> GetAllCustomerGroupFiltersByRetailerIdAsync(string conn, int retailerId)
        {
            var retailerGrpFilters = _cacheClient.GetById<CustomerGroupFilterCache>(retailerId);
            if (retailerGrpFilters == null)
            {
                var filters = await _customerGroupService.GetFiltersByRetailerIdAsync(conn, retailerId);
                if (filters != null && filters.Any())
                {
                    retailerGrpFilters = new CustomerGroupFilterCache();
                    retailerGrpFilters.Id = retailerId;
                    retailerGrpFilters.GroupFilters = filters;
                    _cacheClient.AddOrUpdate(retailerGrpFilters);
                }
                else
                {
                    return null;
                }
            }
            return retailerGrpFilters;
        }

        private void Guard(CustomerChangeInfo customerInfo)
        {
            if (customerInfo == null)
                throw new ArgumentNullException(nameof(customerInfo));
            if (string.IsNullOrEmpty(customerInfo.ConnectionString))
            {
                throw new Exception("Invalid message: connection string");
            }
            if (customerInfo.Customer?.RetailerId <= 0)
            {
                throw new Exception("Invalid message: require retailerId");
            }
            if (customerInfo.Customer?.Id <= 0)
            {
                throw new Exception("Invalid message: require customerId");
            }
        }

        public async Task BulkProcessCustomerInfoChange(CustomerBulkChangeInfo bulkChangeInfo)
        {
            Guard(bulkChangeInfo);
            var customers = bulkChangeInfo.Customers;
            await AutoSwitchCustomerGroupAsync(bulkChangeInfo.RetailerId, customers, bulkChangeInfo);
        }

        private async Task AutoSwitchCustomerGroupAsync(int retailerId, IList<Customer> customers, KvContext context)
        {
            var filter = await GetAllCustomerGroupFiltersByRetailerIdAsync(context.ConnectionString, retailerId);
            if (filter?.GroupFilters == null || !filter.GroupFilters.Any())
            {
                return;
            }
            await _customerGroupService.BulkAutoChangeCustomerGroupAsync(context, customers, filter.GroupFilters);
        }

        private void Guard(CustomerBulkChangeInfo bulkChangeInfo)
        {
            if (bulkChangeInfo == null)
                throw new ArgumentNullException(nameof(bulkChangeInfo));
            if (bulkChangeInfo.Customers == null || !bulkChangeInfo.Customers.Any() || bulkChangeInfo.Customers
                    .Select(c => c.Id).GroupBy(id => id).Any(g => g.Count() > 1))
            {
                throw new Exception("Invalid CustomerBulkChangeInfo message: customers");
            }
            if (string.IsNullOrEmpty(bulkChangeInfo.ConnectionString))
            {
                throw new Exception("Invalid CustomerBulkChangeInfo message: connection string");
            }
        }

        public async Task ProcessGroupChange(CustomerGroupChangeInfo groupChangeInfo)
        {
            await Guard(groupChangeInfo);
            if (groupChangeInfo.IsDeleteAction)
            {
                RemoveCustomerGroupFilterCache(groupChangeInfo.CustomerGroup);
                return;
            }
            var typeUpdate = (CustomerGroupUpdateType)groupChangeInfo.CustomerGroup.TypeUpdate;
            var filterConditions = groupChangeInfo.CustomerGroup.Filter?.FromJson<List<FilterObject>>();
            var filter = CustomerGroupHelper.ExtractCustomerGroupFilterValue(filterConditions);
            filter.CustomerGroupId = groupChangeInfo.CustomerGroup.Id;
            filter.GroupName = groupChangeInfo.CustomerGroup.Name;
            filter.TypeUpdate = typeUpdate;
            await UpdateCustomerGroupFilterCache(groupChangeInfo.ConnectionString, groupChangeInfo.CustomerGroup.RetailerId, filter);
            if (groupChangeInfo.IsNeedRefactor)
            {
                await _customerGroupService.RefactorGroupMemberAsync(groupChangeInfo, groupChangeInfo.CustomerGroup.RetailerId, filter);
            }
        }

        public async Task<int> AssignGroupByScanBirthday(BirthdayRefactorRequest bfi, Action<string> onErrorAction)
        {
            return await _customerGroupService.AssignGroupByScanBirthday(bfi, bfi.StartDate, bfi.StartMonth, bfi.EndDate, bfi.EndMonth, onErrorAction);
        }

        public async Task<int> UpdateMissingCustomerSummaryInfo(MissingInfoScanRequest scanRequest, Action<string> onErrorAction)
        {
            var customers = await _customerGroupService.UpdateMissingCustomerSummaryInfo(scanRequest, scanRequest.TimeSpan);
            var retailerIds = customers.Select(c => c.RetailerId).Distinct();
            var dictFilters = new Dictionary<int, CustomerGroupFilterCache>();
            foreach (var retailerId in retailerIds)
            {
                dictFilters[retailerId] = await GetAllCustomerGroupFiltersByRetailerIdAsync(scanRequest.ConnectionString, retailerId);
            }

            foreach (var customer in customers)
            {
                try
                {
                    dictFilters.TryGetValue(customer.RetailerId, out var filterCache);
                    if (filterCache == null)
                    {
                        continue;
                    }

                    scanRequest.Context = new ExecutionContext
                    {
                        RetailerId = customer.RetailerId,
                        User = new SessionUser
                        {
                            Id = 0,
                            UserName = "System"
                        }
                    };
                    await _customerGroupService.AutoChangeCustomerGroupAsync(scanRequest, customer, filterCache.GroupFilters);
                }
                catch (Exception e)
                {
                    onErrorAction?.Invoke(e.ToString());
                }
            }
            return customers.Count;
        }

        private void RemoveCustomerGroupFilterCache(CustomerGroup customerGroup)
        {
            var retailerId = customerGroup.RetailerId;
            var groupId = customerGroup.Id;
            if (retailerId <= 0 || groupId <= 0)
            {
                return;
            }
            var existingFilter = _cacheClient.GetById<CustomerGroupFilterCache>(retailerId);
            if (existingFilter == null)
            {
                return;
            }
            existingFilter.GroupFilters =
                existingFilter.GroupFilters.Where(f => f.CustomerGroupId != groupId).ToList();
            _cacheClient.AddOrUpdate(existingFilter);
        }

        private async Task UpdateCustomerGroupFilterCache(string conn, int retailerId, CustomerGroupFilter filter)
        {
            if (retailerId <= 0 || filter == null)
            {
                return;
            }

            var existingFilter = await GetAllCustomerGroupFiltersByRetailerIdAsync(conn, retailerId) ??
                                 new CustomerGroupFilterCache {GroupFilters = new List<CustomerGroupFilter>(), Id = retailerId };

            var allGroupFilter = existingFilter.GroupFilters.Where(f => f.CustomerGroupId != filter.CustomerGroupId)
                .ToList();
            if (filter.TypeUpdate.HasFlag(SystemAuto) && filter.TypeUpdate > 0 && filter.AnyFilterHasValue())
            {
                allGroupFilter.Add(filter);
            }
            else
            {
                allGroupFilter = allGroupFilter.Where(f => f.CustomerGroupId != filter.CustomerGroupId).ToList();
            }
            allGroupFilter = allGroupFilter ?? new List<CustomerGroupFilter>();
            existingFilter.GroupFilters = allGroupFilter;
            _cacheClient.AddOrUpdate(existingFilter);
        }


        private async Task Guard(CustomerGroupChangeInfo groupChangeInfo)
        {
            if (groupChangeInfo == null)
                throw new ArgumentNullException(nameof(groupChangeInfo));
            if (string.IsNullOrEmpty(groupChangeInfo.ConnectionString))
            {
                throw new Exception("Invalid message: connection string");
            }
            if (groupChangeInfo.CustomerGroup?.RetailerId <= 0)
            {
                throw new Exception("Invalid message: require retailerId");
            }
            if (groupChangeInfo.CustomerGroup?.Id <= 0)
            {
                throw new Exception("Invalid message: require CustomerGroupId");
            }
            if (!groupChangeInfo.IsDeleteAction)
            {
                var customerGroup = await _customerGroupService.GetByIdAsync(groupChangeInfo, groupChangeInfo.CustomerGroup.Id);
                if (customerGroup == null || customerGroup.RetailerId != groupChangeInfo.CustomerGroup.RetailerId)
                {
                    throw new Exception($"Not match CustomerGroup and Retailer {groupChangeInfo.ToJson()}");
                }
            }
            var retailer = _retailerService.GetById(groupChangeInfo.ConnectionString, groupChangeInfo.CustomerGroup?.RetailerId ?? 0);
            if (retailer == null)
            {
                throw new Exception($"Retailer Null {groupChangeInfo.ToJson()}");
            }
        }
    }
}