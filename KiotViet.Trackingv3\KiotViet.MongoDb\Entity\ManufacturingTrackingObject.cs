﻿using System;
using KiotViet.Data.Entity;
using KiotViet.MongoDb.Interface;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class ManufacturingTrackingObject : InventoryTracking, IEntityId, ICreatedOn, ITimeMesure, IHistory
    {
        //db.ManufacturingTrackingObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.ManufacturingTrackingObject.createIndex( { "RetailerId": 1, "DocumentId": 1, "DocumentType": 1 })
        //db.ManufacturingTrackingObject.createIndex( { "RetailerId": 1, "BranchId": 1, "ProductId": 1 })
        public ManufacturingTrackingObject(InventoryTracking o)
        {
            if (o == null) return;
            CopyFrom(o);
        }

        public DateTime CreatedAt { get; set; }
        public string TimeMesure { get; set; }
        public string History { get; set; }
        public ObjectId _id { get; set; }
    }
}