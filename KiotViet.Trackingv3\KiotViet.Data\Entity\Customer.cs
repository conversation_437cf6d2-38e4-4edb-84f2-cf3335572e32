﻿using System;
using KiotViet.Data.Interface;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class Customer : ILongIdentifiable
    {
        [PrimaryKey]
        public long Id { get; set; }
        public decimal? Debt { get; set; }
        public DateTime? LastTradingDate { get; set; }
        public long? RewardPoint { get; set; }
        public byte Type { get; set; }
        public string Code { get; set; }
        public string Name { get; set; }
        public bool? Gender { get; set; }
        public DateTime? BirthDate { get; set; }
        public string TaxCode { get; set; }
        public int? LocationId { get; set; }
        public int? BranchId { get; set; }
        public int RetailerId { get; set; }

        public decimal? TotalInvoiced { get; set; }
        public long? TotalPoint { get; set; }
        public decimal? TotalReturn { get; set; }
        public int? PurchaseNumber { get; set; }

        [Ignore]
        public decimal TotalRevenue => (TotalInvoiced ?? 0) - (TotalReturn ?? 0);

        public Customer()
        {

        }

        public Customer(Customer cus)
        {
            Id = cus.Id;
            Debt = cus.Debt;
            LastTradingDate = cus.LastTradingDate;
            RewardPoint = cus.RewardPoint;
            Type = cus.Type;
            Code = cus.Code;
            Name = cus.Name;
            Gender = cus.Gender;
            BirthDate = cus.BirthDate;
            TaxCode = cus.TaxCode;
            LocationId = cus.LocationId;
            BranchId = cus.BranchId;
            RetailerId = cus.RetailerId;
            TotalInvoiced = cus.TotalInvoiced;
            TotalPoint = cus.TotalPoint;
            TotalReturn = cus.TotalReturn;
            PurchaseNumber = cus.PurchaseNumber;
        }
    }
}