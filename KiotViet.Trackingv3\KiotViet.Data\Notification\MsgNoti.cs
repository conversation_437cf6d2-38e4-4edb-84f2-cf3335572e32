﻿using System;

namespace KiotViet.Data.Notification
{
    public class MsgNoti
    {
        public string DocumentCode { get; set; }
        public long ActionByUserId { get; set; }
        public string ActionByUserName { get; set; }
        public int ToBranchId { get; set; }
        public string ToBranchName { get; set; }
        public int? GroupId { get; set; }
        public int Value { get; set; }
        public string ResponseError { get; set; }
        public int Status { get; set; } = 1;
        public bool IsSystem { get; set; }
        public bool IsSent { get; set; } = true;
        public int NotifyUser { get; set; }
        public int UserId { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int IndustryId { get; set; }
        public string DocumentItem { get; set; }
        public string DocumentImage { get; set; }
        public int? TableId { get; set; }
        public string TableName { get; set; }
        public string TableGroupName { get; set; }
        public string Payload { get; set; }
        public string ToDevice { get; set; }
        public string NotificationUrl { get; set; }
        public string NotificationTitle { get; set; }
    }

    public class MainMsgNoti : MsgNoti
    {
        public Guid MessageId { get; set; }
        public int BranchId { get; set; }
        public string BranchName { get; set; }
        public int RetailerId { get; set; }
        public string NotifyMessage { get; set; }//{Tên đầy đủ của hàng hóa} dưới định mức tồn tại {Tên chi nhánh}.
        public string EventType { get; set; }//Product_OnHand
        public DateTime CreatedDate { get; set; }
        public int Shard { get; set; }
        public long? DocumentId { get; set; }
        public string Extra { get; set; }
    }

    public class Msg
    {
        public MainMsgNoti Data { get; set; }
        public string Id { get; set; }
        public int Type { get; set; } = 2;
    }
}