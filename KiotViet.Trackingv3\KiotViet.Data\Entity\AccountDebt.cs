﻿using System;
using System.Collections.Generic;
using System.Text;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class AccountDebt
    {
        [PrimaryKey]
        [AutoIncrement]
        public long Id { get; set; }
        public string AccountType { get; set; }
        public long AccountId { get; set; }
        public decimal Debt { get; set; }
        public int RetailerId { get; set; }
    }
}
