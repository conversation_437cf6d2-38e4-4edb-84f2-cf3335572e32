using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using KiotViet.Service.Helper;
using KiotViet.Service.Interface;
using KiotViet.Services.Common;
using KiotViet.Util;
using Microsoft.Extensions.Logging;
using ServiceStack;
using ServiceStack.Data;

namespace KiotViet.Service.Impl
{
    public class InventoryTrackingHandler : BaseHandler, ITrackingHandler<InventoryImpact, InventoryTrackingHandler>
    {
        protected override BunchOfEnum.TrackingType TrackingType => BunchOfEnum.TrackingType.InventoryTracking;

        public InventoryTrackingHandler(
            IDbConnectionFactory connectionFactory,
            ITrackingHelper trackingHelper,
            IMongoService mongoService,
            ILogger<InventoryTrackingHandler> logger) 
            : base(connectionFactory, trackingHelper, mongoService, logger)
        {
        }

        public async Task Process(TrackingTransaction msgTracking, CancellationToken cancellationToken = default)
        {
            if (await IsMessageProcessedAsync(msgTracking.GuidKey, cancellationToken))
            {
                _logger.LogWarning(new ProcessedTrackingException($"Message {msgTracking.PartitionKey} is handled"), "Message is handled");
                return;
            }

            var inventoryImpactList = msgTracking.ListImpacts.Cast<InventoryImpact>().ToList();
            if (inventoryImpactList.Any())
            {
                foreach (var inventoryImpact in inventoryImpactList)
                {
                    await ProcessEachInventoryImpactAsync(inventoryImpact, cancellationToken);
                }
            }

            await ProcessMessageAsync(msgTracking.GuidKey, cancellationToken);
        }

        private async Task ProcessEachInventoryImpactAsync(InventoryImpact inventoryImpact, CancellationToken cancellationToken)
        {
            try
            {
                if (cancellationToken.IsCancellationRequested) cancellationToken.ThrowIfCancellationRequested();

                CheckIfRetailerIsIgnored(inventoryImpact);
                await _trackingHelper.ProcessInventoryImpact(inventoryImpact, 0, cancellationToken);
            }
            catch (ImpactUpdateWarningException ex)
            {
                _logger.LogWarning(ex, "Impact can't update because the quantity record is too large");
            }
            catch (IgnoreRetailerWarningException ex)
            {
                _logger.LogWarning(ex, KvStatic.RecoveryMsg);
            }
            catch (Exception ex)
            {
                inventoryImpact.IsFailed = true;
                _logger.LogError(ex, $"{ex.Message} - {inventoryImpact.ToJson()}");
            }
        }

        private static void CheckIfRetailerIsIgnored(InventoryImpact inventoryImpact)
        {
            var retailerIdsExclude = ConvertHelper.ToString(KvAppConfig.KvTrackingConfigurationBuilder["RetailerIdsIgnoreInventoryTracking"])
                          .Split(",", StringSplitOptions.RemoveEmptyEntries)
                          .Select(x => ConvertHelper.ToInt32(x)).ToList();
            // nếu không xử lí thì throw exception để không tạo bản ghi EventTrackingDetail có status = 1
            if (retailerIdsExclude.Contains(inventoryImpact.RetailerId))
                throw new IgnoreRetailerWarningException($"{KvStatic.RecoveryMsg} - {inventoryImpact.ToJson()}");
        }
    }
}
