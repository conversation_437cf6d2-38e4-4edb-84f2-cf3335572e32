﻿using System;
using System.Collections.Generic;
using System.Linq;
using KiotViet.Data.Entity;
using KiotViet.MongoDb.Interface;
using KiotViet.Services.Common;
using MongoDB.Bson;

namespace KiotViet.MongoDb.Entity
{
    public class PointTrackingObject : IEntityId, ICreatedOn, ITimeMesure, IHistory, ITotalTime, IEventType, ITrackingStep
    {
        //db.PointTrackingObject.createIndex( { "CreatedAt": 1 }, { expireAfterSeconds: 2592000 } ) // production: 2592000 seconds ~ 30 days // 30 * 24 * 60 * 60
        //db.PointTrackingObject.createIndex( { "Data.RetailerId": 1, "Data.DocumentId": 1, "Data.DocumentType": 1 })
        //db.PointTrackingObject.createIndex( { "Data.RetailerId": 1, "Data.PartnerId": 1, "Data.DataZone": 1 })

        public PointTrackingObject(IEnumerable<PointImpact> listBalanceImpacts)
        {
            Steps = new List<TrackingStep>();
            Data = new List<PointTracking>();
            if (listBalanceImpacts == null) return;
            foreach (var item in listBalanceImpacts.Select(x => x.Track))
            {
                Data.Add(new PointTracking()
                {
                    PartnerId = item.PartnerId,
                    DocumentId = item.DocumentId,
                    DocumentCode = item.DocumentCode,
                    DocumentType = item.DocumentType,
                    Description = item.Description,
                    Value = item.Value,
                    Balance = item.Balance,
                    RetailerId = item.RetailerId,
                    TransDate = item.TransDate,
                    DataZone = item.DataZone
                });
            }
        }

        public ObjectId _id { get; set; }
        public DateTime CreatedAt { get; set; }
        public string TimeMesure { get; set; }
        public string History { get; set; }
        public List<PointTracking> Data { get; set; }
        public double TotalTime { get; set; }
        public string EventType => nameof(PointTrackingObject);
        public List<TrackingStep> Steps { get; set; }
    }
}