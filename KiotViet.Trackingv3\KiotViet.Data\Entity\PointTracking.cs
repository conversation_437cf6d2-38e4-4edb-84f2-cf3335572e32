﻿using System;
using ServiceStack.DataAnnotations;

namespace KiotViet.Data.Entity
{
    public class PointTracking
    {
        public PointTracking() { }
        public PointTracking(PointTracking source) : this()
        {
            CopyFrom(source);
        }
        [PrimaryKey]
        public long PartnerId { get; set; }
        [PrimaryKey]
        public long DocumentId { get; set; }
        public string DocumentCode { get; set; }
        [PrimaryKey]
        public int DocumentType { get; set; }
        public string Description { get; set; }
        public decimal Amount { get; set; }
        public long Value { get; set; }
        public long Balance { get; set; }
        public int RetailerId { get; set; }
        public System.DateTime TransDate { get; set; }
        [PrimaryKey]
        public int DataZone { get; set; }
        [Ignore]
        public Guid KeyBig { get; set; }
        public void CopyFrom(PointTracking source)
        {
            PartnerId = source.PartnerId;
            DocumentId = source.DocumentId;
            DocumentCode = source.DocumentCode;
            DocumentType = source.DocumentType;
            Description = source.Description;
            Value = source.Value;
            Balance = source.Balance;
            RetailerId = source.RetailerId;
            TransDate = source.TransDate;
            DataZone = source.DataZone;
        }
    }
    public class DummyPointTracking : PointTracking
    {
    }
}
