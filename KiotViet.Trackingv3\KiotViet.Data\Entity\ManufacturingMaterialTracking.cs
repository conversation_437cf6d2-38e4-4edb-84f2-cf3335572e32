﻿using KiotViet.Data.Interface;

namespace KiotViet.Data.Entity
{
    public class Manufacturing : ILongIdentifiable
    {
        public long Id { get; set; }
        public string Code { get; set; }
        public long ProductId { get; set; }
        public double Quantity { get; set; }
        public int RetailerId { get; set; }
        public int BranchId { get; set; }
        public System.DateTime ManufacturingDate { get; set; }
    }
    public class ManufacturingMaterialTracking : ILongIdentifiable
    {
        public long Id { get; set; }
        public long MaterialId { get; set; }
        public long ManufacturingId { get; set; }
        public double Quantity { get; set; }
    }

    public class ManufacturingMaterialTrackingShort
    {
        public long ProductId { get; set; }
        public decimal Cost { get; set; }
        public double Quantity { get; set; }
    }
}